<div class="form-horizontal">
    <hr />
    <div class="col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-4 col-md-4 col-lg-4 control-label">
                        <span ng-show="!vm.isVisualizar()" class="text-danger mr-5">*</span>Forma de pagamento:
                    </label>
                    <div class="input-group col-xs-12 col-sm-8 col-md-8 col-lg-8">
                        <ui-select name="FormaPagamento" ng-model="vm.painelCiot.pagamento.formaPagamento"
                            ats-ui-select-validator validate-on="blur"
                            ng-change="vm.FormaPagamento(vm.painelCiot.pagamento.formaPagamento)"
                            ng-disabled="vm.disabledFields() || !vm.isNew()"
                            required-message="'Forma de pagamento é obrigatório'" required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboFormaPag.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" ng-show="vm.painelCiot.pagamento.formaPagamento === 0">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-4 col-md-4 col-lg-4 control-label"><span
                            class="text-danger mr-5">*</span>Agência:</label>
                    <div class="input-group col-xs-12 col-sm-8 col-md-8 col-lg-8">
                        <input type="text" maxlength="10" ng-disabled="vm.disabledFields()" ats-numeric
                            onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                            ng-model="vm.painelCiot.agencia" name="Agencia" class="form-control" validate-on="blur"
                            required-message="'Agência é obrigatória'"
                            ng-required="vm.painelCiot.pagamento.formaPagamento === 0" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="vm.painelCiot.pagamento.formaPagamento === 0">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-4 col-md-4 col-lg-4 control-label"><span
                            class="text-danger mr-5">*</span>Conta:</label>
                    <div class="input-group col-xs-12 col-sm-8 col-md-8 col-lg-8 field-conta-pagamento">
                        <div class="input-group">
                            <input type="text" maxlength="15" ng-model="vm.painelCiot.conta" name="Conta"
                                ng-disabled="vm.disabledFields()" maxlength="15" ats-numeric
                                onkeypress="return event.charCode >= 48 && event.charCode <= 57" class="form-control"
                                validate-on="blur" ng-required="vm.painelCiot.pagamento.formaPagamento === 0" />
                        </div>
                        <div class="input-group-addon" style="border-color: transparent;">-</div>
                        <div class="input-group">
                            <input type="text" maxlength="2" ng-model="vm.painelCiot.verificadorConta" ats-numeric
                                onkeypress="return event.charCode >= 48 && event.charCode <= 57" name="Verificador"
                                ng-disabled="vm.disabledFields()" class="form-control" validate-on="blur"
                                required-message="'DV é obrigatório'"
                                ng-required="vm.painelCiot.pagamento.formaPagamento === 0" />
                        </div>

                    </div>
                </div>
            </div>
            <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-4 col-md-4 col-lg-4'"
                ng-disabled="vm.disabledFields()" name="Banco" idname="Banco" idmodel="Banco"
                tabledefinition="vm.consultaBanco" label="'Banco:'" placeholder="'Selecione um Banco'"
                ngshowasterisc="vm.painelCiot.pagamento.formaPagamento===0" required-message="'Banco é obrigatório'"
                ng-required="vm.painelCiot.pagamento.formaPagamento===0">
            </consulta-padrao-modal>
        </div>
        <div class="row" ng-show="vm.painelCiot.pagamento.formaPagamento === 0">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-4 col-md-4 col-lg-4 control-label"><span
                            class="text-danger mr-5">*</span>Tipo conta:</label>
                    <div class="input-group col-xs-12 col-sm-8 col-md-8 col-lg-8">
                        <input type="text" maxlength="10" ng-model="vm.painelCiot.tipoConta" name="TipoConta"
                            ng-disabled="vm.disabledFields()" class="form-control" validate-on="blur"
                            required-message="'Tipo conta é obrigatória'" ats-numeric
                            onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                            ng-required="vm.painelCiot.pagamento.formaPagamento === 0" />
                    </div>
                </div>
            </div>
        </div>
        <br>
        <div class="row align-div-veiculo" ng-show="vm.isVisualizar() && vm.painelCiot.parcelasPagamento.length > 0">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>Parcelas</h5>
                    <div ibox-tools></div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="table-responsive">
                            <table class="table table-striped col-xs-12">
                                <thead>
                                    <tr>
                                        <th scope="col">Código parcela</th>
                                        <th scope="col">Valor</th>
                                        <th scope="col">Data previsão pagamento</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="pagamento in vm.painelCiot.parcelasPagamento">
                                        <td>{{pagamento.codigoParcela}}</td>
                                        <td>{{pagamento.valorParcela}}</td>
                                        <td>{{pagamento.vencimento | date:'dd/MM/yyyy'}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>