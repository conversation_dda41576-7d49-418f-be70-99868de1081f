<style>
    #ParametroCrudController .fixLRpg {
        padding-left: 4px !important;
        padding-right: 4px !important;
        text-align: -webkit-center;
    }

    /* Corrigir espaçamento entre colunas na aba principal */
    #ParametroCrudController .col-sm-6:not(:last-child) {
        padding-right: 15px !important;
    }

    #ParametroCrudController .col-sm-6:not(:first-child) {
        padding-left: 15px !important;
    }

    .widthHF {
        width: 25px;
    }

    .colorGreen {
        color: green;
    }

    .form-wizard>ol>li {
        min-height: 50px;
        padding-top: 10px;
        padding-bottom: 7px;
        background-color: #eeeeee;
        border-color: #eaeaea;
        border-style: solid;
        cursor: pointer;
        border-width: 2px;
    }

    #ParametroCrudController .imgPassCombination {
        position: relative;
        top: 5px;
        right: -37px;
    }

    #ParametroCrudController .ui-select-bootstrap .ui-select-toggle>a.btn {
        position: absolute !important;
        height: 10px !important;
        right: 10px !important;
        margin-top: 0px !important;
    }

    #ParametroCrudController .imgFilial,
    .imgFilialFile {
        position: absolute;
        margin: -20px 105px;
        margin-left: 50%;
        height: 128px;
        width: 128px;
        border-radius: 65%;
        object-fit: cover;
        object-position: center;
    }

    #ParametroCrudController .inputUploadImg {
        position: absolute;
        right: 0;
        top: 117px;
    }

    .form-wizard>ol>li {
        min-height: 33px;
        padding-top: 2px;
        padding-bottom: 0px;
        background-color: #eeeeee;
        border-color: #eaeaea;
        border-style: solid;
        cursor: pointer;
        border-width: 2px;
    }
</style>
<div id="ParametroCrudController" ng-controller="ParametroCrudController as vm">
    <form-header ng-submit="return" items="vm.headerItems" head="'Parâmetros gerais'"></form-header>
    <div class="wrapper-content animated overflow-hidden fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Novo' : 'Editar'}} parâmetro geral</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="frmParametroCrud" role="form" novalidate ats-validator
                            ng-submit="vm.save(frmParametroCrud)" show-validation>

                            <div form-wizard steps="1">
                                <div class="form-wizard">
                                    <ol class="row">
                                        <li ng-click="wizard.go(1)" class="fixLRpg col-sm-12"
                                            ng-class="{'active': wizard.active(1)}">
                                            <h4>Principal</h4>
                                        </li>
                                    </ol>
                                    <br />
                                </div>
                                <div id="activateTab1" ng-show="wizard.active(1)">
                                    <div ng-include="'app/entities/parametros/abas/aba-principal.html'"
                                        class="form-horizontal"> </div>
                                </div>
                            </div>

                            <div class="row clearfix"> </div>
                            <hr-label dark="true"></hr-label>
                            <br />
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-md-12 col-lg-12 text-right">
                                        <button type="button" ng-disabled="vm.isSaving"
                                            ng-click="vm.onClickVoltar(wizard)" class="btn btn-labeled
                                                btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-left"></i>
                                            </span>
                                            Voltar
                                        </button>                                        
                                        <button type="submit" ng-show="wizard.active(1)" ng-disabled="vm.saving"
                                            class="btn btn-labeled btn-success text-right"
                                            data-style="expand-right">
                                            <span class="btn-label">
                                                <i class="fa fa-check-circle"></i>
                                            </span>
                                            Salvar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>