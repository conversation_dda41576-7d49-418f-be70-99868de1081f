(function () {
    'use strict';

    angular.module('bbcWeb').controller('ParametroCrudController', ParametroCrudController);

    ParametroCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$scope', '$timeout', 'PersistentDataService'];

    function ParametroCrudController(
        toastr,
        $rootScope,
        BaseService,
        $state,
        $stateParams,
        $scope,
        $timeout,
        PersistentDataService
    ) {
        var vm = this;

        var selfScope = PersistentDataService.get('ParametroCrudController');

        vm.parametrosTipoString = [
            "CodigoLinkEmpresa",
            "CodigoLinkCiot",
            "UrlComunicacaoConductor",
            "UrlComunicacaoConductorAuth",
            "UrlComunicacaoConductorRegDocs",
            "UrlComunicacaoConductorAliasBank",
            "UrlComunicacaoConductorCompanies",
            "UrlComunicacaoCiot",
            "UrlConfiguracaoWeb",
            "UrlComunicacaoCaptalys",
            "UrlComunicacaoCaptalysRetencao",
            "UrlComunicacaoCaruana",
            "UrlComunicacaoLesing",
            "UrlComunicacaoMobile2You",
            "UrlComunicacaoPixBaas",
            "EmailGestorAbastecimentosMovida",
            "LinkAplicativoCadastroPortador"
        ];

        vm.parametrosTipoCriptografia = [
            "ComunicacaoConductorUsuario",
            "ComunicacaoConductorSenha",
            "ConfiguracaoWebToken",
            "ComunicacaoCaptalysToken",
            "ComunicacaoCaptalysTokenRetencao",
            "ConfiguracaoWebHostName",
            "EmailSmtpClient",
            "EmailUsuario",
            "EmailSenha",
            "ComunicacaoMobile2YouToken",
            "EmpresaPagamento"
        ];
        
        vm.parametrosTipoDecimal = [
            "MargemErroArredondamentoXmlProtocolo",
            "MargemArredondamentoCasasDecimaisLitragemXml"
        ];
        
        vm.parametrosTipoBooleano = [
            "CodigoReenvioPagamentoEvento",
            "AprovarPagamentosAutomaticamente",
            "ForcarGeracaoPagamento",
            "VerificaContigencia",
            "EmailSsl"
        ];

        vm.parametrosComOutraTela = [
            "AprovacaoAutomaticaPrecoCombustivel",
            "ConfiguracaoDeSLA",
            "ConfiguracaoMonitoramentoCIOT",
            "ConfiguracaoValePedagio",
            "ConfiguracaoTelaoSaldo",
            "ConfiguracaoQualificacaoTransacao"
        ];

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Parâmetros gerais',
            link: 'parametros.index'
        }, {
            name: $stateParams.link === 'novo' ? 'Novo' : 'Editar'
        }];

        vm.cmbTipoParametro = [
            { descricao: "Conta de transferência de valor em retenção", data: "CodigoContaTransferenciaValorRetencao" },
            { descricao: "Link empresa", data: "CodigoLinkEmpresa" },
            { descricao: "Tipo de emissão de CIOT", data: "CodigoTipoEmissaoCiot" },
            { descricao: "Período máximo de processamento", data: "CodigoPeriodoMaximoProcessamento" },
            { descricao: "Reenvio de pagamento evento", data: "CodigoReenvioPagamentoEvento" },
            { descricao: "Link CIOT", data: "CodigoLinkCiot" },
            { descricao: "Limite máximo retentativas cancelamento fretes", data: "LimiteMaximoRetentativaFrete" },
            { descricao: "mín. retentativas cancelamento de pagamentos de frete", data: "IntervaloMinimoRetentativaCancelamentoPagamentoFrete" },
            { descricao: "Forçar geração de pagamento abastecimento", data: "ForcarGeracaoPagamento" },
            { descricao: "Numero de retentativas de pagamento", data: "NumeroRetentativaEnvioPagamento" },
            { descricao: "Aprovar pagamentos automaticamente?", data: "AprovarPagamentosAutomaticamente" },
            { descricao: "Prazo maximo para cancelamento de pagamentos de frete", data: "PrazoMaximaParaCancelamentoPagamentoFrete" },
            { descricao: "max. de tentativa de cancelamento de pagamentos de frete", data: "LimiteMaximoRetentativaCancelamentoPagamentoFrete" },
            { descricao: "Período max. de inatividade do portador", data: "PeriodoMaximoInatividadePortador" },
            { descricao: "Período máximo de duração da senha provisória", data: "PeriodoMaximoInatividadeSenhaProvisoria" },
            { descricao: "Sessão expirada - tempo máximo usuário inativo", data: "TempoMaximoUsuarioInativo" },
            { descricao: "Conta de transferência de valor de receitas", data: "CodigoContaCorrenteReceita" },
            { descricao: "Período de duração da senha de acesso", data: "PeriodoDuracaoSenha" },
            { descricao: "Configurações de SLA", data: "ConfiguracaoDeSLA" },
            { descricao: "Conductor URL", data: "UrlComunicacaoConductor" },
            { descricao: "Conductor Autenticação URL", data: "UrlComunicacaoConductorAuth" },
            { descricao: "Conductor Usuário", data: "ComunicacaoConductorUsuario" },
            { descricao: "Conductor Senha", data: "ComunicacaoConductorSenha" },
            { descricao: "Conductor regdocs URL", data: "UrlComunicacaoConductorRegDocs" },
            { descricao: "Conductor aliasbank URL", data: "UrlComunicacaoConductorAliasBank" },
            { descricao: "Conductor companies URL", data: "UrlComunicacaoConductorCompanies" },
            { descricao: "Ciot URL", data: "UrlComunicacaoCiot" },
            { descricao: "URL de configuração WEB", data: "UrlConfiguracaoWeb" },
            { descricao: "Configuração WEB token", data: "ConfiguracaoWebToken" },
            { descricao: "Configuração WEB host", data: "ConfiguracaoWebHostName" },
            { descricao: "Verificar contingência", data: "VerificaContigencia" },
            { descricao: "Captalys URL", data: "UrlComunicacaoCaptalys" },
            { descricao: "Captalys token", data: "ComunicacaoCaptalysToken" },
            { descricao: "Captalys retenção URL", data: "UrlComunicacaoCaptalysRetencao" },
            { descricao: "Captalys retenção token", data: "ComunicacaoCaptalysTokenRetencao" },
            { descricao: "Caruana URL", data: "UrlComunicacaoCaruana" },
            { descricao: "Leasing URL", data: "UrlComunicacaoLesing" },
            { descricao: "E-mail smtp", data: "EmailSmtpClient" },
            { descricao: "E-mail porta", data: "EmailPort" },
            { descricao: "E-mail usuário", data: "EmailUsuario" },
            { descricao: "E-mail senha", data: "EmailSenha" },
            { descricao: "E-mail ssl", data: "EmailSsl" },
            { descricao: "Mobile2You URL", data: "UrlComunicacaoMobile2You" },
            { descricao: "Mobile2You token", data: "ComunicacaoMobile2YouToken" },
            { descricao: "Empresas de pagamento", data: "EmpresaPagamento" },
            { descricao: "Conta de transferência de valor da tarifa em retenção", data: "CodigoContaTransferenciaTarifaValorRetencao" },
            { descricao: "Url de comunicação Caradhras Pix", data: "UrlComunicacaoPixBaas" },
            { descricao: "Aprovação automática atualização preço combustível", data: "AprovacaoAutomaticaPrecoCombustivel" },
            { descricao: "E-mail de notificação de reenvio integração abastecimento MOVIDA", data: "EmailGestorAbastecimentosMovida" },
            { descricao: "Tentativas de reenvio para envio de e-mail de notificação MOVIDA", data: "QuantidadeTentativasReenvioAbastecimentoMovida" },
            { descricao: "Horário para envio de e-mail de notificação MOVIDA", data: "HorarioEnvioEmailGestorAbastecimentosMovida" },
            { descricao:"Dias retroativos para gerar registro de receita", data: "DiasRetroativosGerarReceita" },
            { descricao:"Dia(s) para a não disponibilização da antecipação de recebível", data: "DiasParaNaoDisponibilizacaoAntecipacaoRecebivel" },
            { descricao:"Margem arredondamento casas decimais de valor unitário (XML protocolo)", data:"MargemErroArredondamentoXmlProtocolo" },
            { descricao:"Número do banco", data: "BankNumberDock" },
            { descricao:"Link para download do aplicativo no email", data: "LinkAplicativoCadastroPortador" },
            { descricao:"Tempo de espera reconsulta pix após pagamento", data: "TempoEsperaSegundosPix" },
            { descricao:"Quantidade de reconsulta pix após pagamento", data: "QuantidadeVezesConsultaPix" },
            { descricao:"Tempo de consideração para pix duplicado", data: "TempoRetroativoPixDuplicado" },
            { descricao: "Configuração de monitoramento CIOT", data: "ConfiguracaoMonitoramentoCIOT" },
            { descricao: "Configuração de Vale Pedágio", data: "ConfiguracaoValePedagio" },
            { descricao: "Configuração de tentativa de reenvio pagamento de frete", data: "ConfiguracaoTentativaReenvioPagamentoFrete" },
            { descricao: "Margem arredondamento casas decimais de litragem (XML protocolo)", data:"MargemArredondamentoCasasDecimaisLitragemXml" },
            { descricao: "Configuração do telão de saldo", data: "ConfiguracaoTelaoSaldo" },
            { descricao: "Margem arredondamento casas decimais do valor TOTAL dos abastecimentos (XML protocolo)", data:"MargemErroTotalAbastecimentoXmlProtocolo" },
            { descricao: "Configuração de qualificação de transação", data: "ConfiguracaoQualificacaoTransacao" },
            { descricao: "Número de tentativas de login incorretas para bloqueio do usuário da frota", data:"QuantidadeErroSenhaPortadorFrota" },
            { descricao: "Tempo para reenvio de pagamento com status aberto", data:"TempoReenvioPagamentoStatusAberto" },
            { descricao: "Conta de retenção AR", data: "ContaRetencaoAr" }
        ];

        vm.cmbTipoValor = [
            { descricao: "Texto", data: "String" },
            { descricao: "Número", data: "Number" },
            { descricao: "Criptografado", data: "Criptografia" },
            { descricao:"Decimal", data:"Decimal"}
        ];

        vm.parametro = {};

        vm.menusPai = [];

        vm.exibirTextBox = true;

        vm.exibirTipoValor = true;

        vm.removeCaractereEspecial = function () {
            vm.parametro.valor = vm.parametro.valor.replace(/[^0-9]/g, '');
        }

        vm.isNew = function () {
            return $stateParams.link === 'novo';
        };

        vm.loadEdit = function (request) {
            var received = JSON.parse(request);

            vm.parametro.id = received.id;
            vm.parametro.infoAdicional = received.infoAdicional;
            vm.parametro.tipoParametros = received.tipoParametros;
            vm.parametro.tipoValor = received.tipoValor;
            vm.parametro.valor = received.valor;
            

            if (vm.parametrosTipoBooleano.includes(received.tipoParametros)) {
                vm.parametro.valor = received.valor === "1"
                return
            }
            
            if (vm.parametro.tipoParametros === "HorarioEnvioEmailGestorAbastecimentosMovida") {
                vm.parametro.valor = new Date(1, 1, 1, received.valor, 0, 0, 0)
            }
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving) return;

            if (vm.parametro.tipoParametros === "HorarioEnvioEmailGestorAbastecimentosMovida") {
                vm.parametro.valor = vm.parametro.valor.getHours()
                if (parseInt(vm.parametro.valor) < 0 || parseInt(vm.parametro.valor) > 23) {
                    toastr.error('Por favor escolha um valor entre 0 e 23 para o horário do dia em que serão feitos os envios das notificações.');
                    return;
                }
            }

            vm.objToSave = {}

            angular.copy(vm.parametro, vm.objToSave)

            vm.objToSave.valor = vm.parametro.valor.toString();

            if (vm.parametrosTipoBooleano.includes(vm.objToSave.tipoParametros)) {
                vm.objToSave.valor = vm.parametro.valor === true ? '1' : '0'
            }
        
            if (vm.parametrosComOutraTela.includes(vm.objToSave.tipoParametros)) {
                vm.objToSave.ReferenciaId = -2
                vm.objToSave.valor = 'Este parametro nao precisa de valor';
            } else {
                vm.objToSave.ReferenciaId = -1;
            }

            if (vm.parametro.id === "Auto") {
                vm.objToSave.id = "0";
            }

            vm.isSaving = true;

            BaseService.post('Parametros', 'Cadastrar', vm.objToSave).then(function (response) {
                vm.isSaving = false;
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('parametros.index');
                } else
                    toastr.error(response.message);
            });
        };

        vm.verificarTipoParametro = function () {
            if (vm.parametrosTipoString.includes(vm.parametro.tipoParametros)) {
                vm.parametro.tipoValor = "String";
            }

            else if (vm.parametrosTipoCriptografia.includes(vm.parametro.tipoParametros)) {
                vm.parametro.tipoValor = "Criptografia";
            }

            else if(vm.parametrosTipoDecimal.includes(vm.parametro.tipoParametros)){
                vm.parametro.tipoValor = "Decimal";
            }else {
                vm.parametro.tipoValor = "Number";
            }

            vm.clearValor();
        }

        vm.clearValor = function () {
            vm.parametro.valor = "";
        }

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador === true;
        };

        vm.onClickVoltar = function (wizard) {
            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex === 1)
                $state.go('parametros.index');
        };

        $scope.$watch('vm.parametro.tipoParametros', function (value) {
            vm.exibirTextBox = !(vm.parametrosTipoBooleano.includes(vm.parametro.tipoParametros) || vm.parametrosComOutraTela.includes(vm.parametro.tipoParametros));
        });

        $scope.$watch('vm.parametro.tipoParametros', function (value) {
            vm.exibirTipoValor = !vm.parametrosComOutraTela.includes(vm.parametro.tipoParametros);
        });

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'parametros.index')
                PersistentDataService.remove('ParametroCrudController');
            else
                PersistentDataService.store('ParametroCrudController', vm, "Cadastro - Parâmetros gerais", null, "parametros.parametros-crud", vm.parametro.id);
        });

        if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);
        }
        else if (vm.isNew()) {
            vm.parametro.id = 'Auto';
        }
        else {
            vm.loadEdit($stateParams.link);
        }

        $timeout(function () {
            PersistentDataService.remove('ParametroController');
        }, 15);
    }
})();