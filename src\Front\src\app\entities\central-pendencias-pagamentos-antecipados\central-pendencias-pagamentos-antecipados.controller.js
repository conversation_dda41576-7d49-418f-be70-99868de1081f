(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('CentralPendenciasPagamentosAntecipadosController', CentralPendenciasPagamentosAntecipadosController);

    CentralPendenciasPagamentosAntecipadosController.inject = [
        'BaseService',
        '$rootScope',
        '$window',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        '$uibModal',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert',
        'uiGridConstants'
    ];

    function CentralPendenciasPagamentosAntecipadosController(
        BaseService, 
        $rootScope, 
        $window,
        toastr, 
        $scope,
        PersistentDataService, 
        $timeout, 
        $state,
        $uibModal,
        PERFIL_ADMINISTRADOR,
        SweetAlert,
        uiGridConstants) {
        
        var vm = this;
        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Central de pendências' }];

        vm.primeiraConsulta = true;
        vm.totalPagamentos = 0;
        vm.totalSemana = 0;
        vm.totalMes = 0;
        vm.totalPeriodoSelecionado = 0;
        vm.filtrosExpandidos = true;
        vm.semDados = false;

        var alertaFechadoStorage = localStorage.getItem('centralPendenciasAlertaFechado');
        vm.alertaFechado = alertaFechadoStorage === 'true';

        if ($rootScope.usuarioLogado.empresaId == null || $rootScope.usuarioLogado.empresaId == undefined) {
            vm.usuAdm = true;
        } else {
            vm.usuAdm = null;
        }

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        // Configuração da consulta de empresa
        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            },
            executeAfterSelection: function () {
                vm.consultar();
                vm.limparTotais();
            },
            clearFunction: function () {
                vm.filtros.empresaId = null;
                vm.consultar();
                vm.limparTotais();
            }
        };

        $scope.$watch('vm.consultaEmpresa.selectedValue', function (newValue, oldValue) {
            if (newValue !== oldValue) {
                vm.filtros.empresaId = vm.consultaEmpresa.selectedValue;
            }
        });

        // Configuração do DateRangePicker
        vm.dateOptions = {
            timePicker: false,
            timePicker24Hour: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Hoje': [moment(), moment()],
                'Ontem': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Últimos 7 dias': [moment().subtract(6, 'days'), moment()],
                'Últimos 30 dias': [moment().subtract(29, 'days'), moment()],
                'Este mês': [moment().startOf('month'), moment().endOf('month')],
                'Mês passado': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        };

       vm.filtros = {
            periodo: {
                startDate: moment().startOf('month'),
                endDate: moment().endOf('month')
            },
            empresaId: null,
            cpfCnpjProprietario: ''
        };

        vm.dateRangeOptions = {
            locale: {
                applyLabel: "Aplicar",
                cancelLabel: "Cancelar",
                fromLabel: "De",
                toLabel: "Até",
                customRangeLabel: "Personalizado",
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'],
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
                    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
                firstDay: 1
            },
            ranges: {
                'Hoje': [moment(), moment()],
                'Ontem': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Últimos 7 dias': [moment().subtract(6, 'days'), moment()],
                'Últimos 30 dias': [moment().subtract(29, 'days'), moment()],
                'Este mês': [moment().startOf('month'), moment().endOf('month')],
                'Mês passado': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        };

        vm.gridOptions = {
            data: [],
            enableFiltering: true,
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            enableHorizontalScrollbar: 1, // Habilita scroll horizontal
            enableVerticalScrollbar: 1,   // Habilita scroll vertical
            urlRelatorio: "Viagem/ConsultarGridCentralPendenciaPagamentosAntecipados",
            dataSource: {
                url: "Viagem/ConsultarGridCentralPendenciaPagamentosAntecipados",
                params: function() {
                    var inicio = vm.filtros.periodo.startDate.toDate();
                    var fim = vm.filtros.periodo.endDate.toDate();
                    return {
                        dataInicial: new Date(inicio.getTime() - (inicio.getTimezoneOffset() * 60000)).toJSON(),
                        dataFinal: new Date(fim.getTime() - (fim.getTimezoneOffset() * 60000)).toJSON(),
                        empresaId: vm.filtros.empresaId,
                        cpfCnpjProprietario: vm.filtros.cpfCnpjProprietario
                    };
                },
                consultarDadosRelatorio: function(callback) {
                    var params = this.params();
                    BaseService.get("Viagem/ConsultarGridCentralPendenciaPagamentosAntecipados", params).then(callback);
                },
                events: {
                    onDataBound: function(data) {
                        // Este evento sempre executa, mesmo quando não há dados
                        console.log('🔥 onDataBound executado! Data:', data);

                        // Fazer uma nova requisição para pegar os totais quando não há dados na grid
                        if (!data || data.length === 0) {
                            var params = vm.gridOptions.dataSource.params();
                            BaseService.get("Viagem/ConsultarGridCentralPendenciaPagamentosAntecipados", params).then(function(response) {
                                console.log('🔥 Requisição manual para totais:', response);
                                if (response.data) {
                                    vm.totalPagamentos = response.data.totalPagamentos || 0;
                                    vm.totalSemana = response.data.totalSemana || 0;
                                    vm.totalMes = response.data.totalMes || 0;
                                    vm.totalPeriodoSelecionado = response.data.totalPeriodoSelecionado || 0;
                                    vm.semDados = true;

                                    console.log('=== TOTAIS MANUAL ===');
                                    console.log('Total período selecionado:', vm.totalPeriodoSelecionado);
                                    console.log('Sem dados:', vm.semDados);
                                    console.log('====================');
                                }
                            });
                        } else {
                            vm.semDados = false;
                        }
                    }
                }
            },
            callBack: function(response) {
                if (response.data) {
                    vm.totalPagamentos = response.data.totalPagamentos || 0;
                    vm.totalSemana = response.data.totalSemana || 0;
                    vm.totalMes = response.data.totalMes || 0;
                    vm.totalPeriodoSelecionado = response.data.totalPeriodoSelecionado || 0;

                    // Verificar se há dados na grid
                    vm.semDados = !response.data.items || !Array.isArray(response.data.items) || response.data.items.length === 0;

                    console.log('=== TOTAIS DO BACKEND ===');
                    console.log('Response data items:', response.data.items);
                    console.log('Items é array?:', Array.isArray(response.data.items));
                    console.log('Items length:', response.data.items ? response.data.items.length : 'undefined');
                    console.log('Items real length:', response.data.items && response.data.items.length);
                    console.log('Total geral:', vm.totalPagamentos);
                    console.log('Total semana atual:', vm.totalSemana);
                    console.log('Total mês atual:', vm.totalMes);
                    console.log('Total período selecionado:', vm.totalPeriodoSelecionado);
                    console.log('Sem dados (vm.semDados):', vm.semDados);
                    console.log('========================');
                } else {
                    vm.semDados = true;
                }
            },
            columnDefs: [
                {
                    name: 'acoes',
                    displayName: 'Ações',
                    cellTemplate: '<div class="ui-grid-cell-contents text-center">' +
                        '<button class="btn btn-xs btn-info" ng-show="row.entity.statusDescricao !== \'Aprovado\'" ng-click="grid.appScope.vm.visualizarDetalhes(row.entity)" title="Visualizar Detalhes">' +
                        '<i class="fa fa-eye"></i>' +
                        '</button>' +
                        '</div>',
                    width: 80,
                    enableSorting: false,
                    enableFiltering: false
                },
                { name: 'id', displayName: 'Código Pagamento', width: 120, serverField: 'id',enableFiltering: true },
                { name: 'viagemId', displayName: 'Código Viagem', width: 120, enableFiltering: false },
                { name: 'pagamentoExternoId', displayName: 'PagamentoExternoId', width: 150, enableFiltering: true },
                { name: 'viagemExternoId', displayName: 'ViagemExternoId', width: 150, enableFiltering: true },
                {
                    name: 'status',
                    displayName: 'Status',
                    cellTemplate: '<div class="ui-grid-cell-contents">' +
                        '<span ng-class="{' +
                            '\'label label-success\': row.entity.status === 0 || row.entity.statusDescricao === \'Fechado\' || row.entity.statusDescricao === \'Aprovado\',' +
                            '\'label label-warning\': row.entity.status === 1 || row.entity.statusDescricao === \'Aberto\',' +
                            '\'label label-info\': row.entity.status === 2 || row.entity.statusDescricao === \'Pendente\',' +
                            '\'label label-danger\': row.entity.status === 3 || row.entity.statusDescricao === \'Erro\',' +
                            '\'label label-default\': row.entity.status === 4 || row.entity.statusDescricao === \'Cancelado\',' +
                            '\'label label-primary\': row.entity.status === 5 || row.entity.statusDescricao === \'Processando\',' +
                            '\'label label-muted\': row.entity.status === 6 || row.entity.statusDescricao === \'Não Executado\'' +
                        '}">' +
                        '{{row.entity.statusDescricao}}' +
                        '</span>' +
                        '</div>',
                    width: 100
                },
                {
                    name: 'statusAntecipacaoParcelaProprietario',
                    displayName: 'Status Antecipação',
                    cellTemplate: '<div class="ui-grid-cell-contents">' +
                        '<span class="label" ng-class="{' +
                            '\'label-info\': row.entity.statusAntecipacaoParcelaProprietario === \'AguardandoProcessamento\',' +
                            '\'label-success\': row.entity.statusAntecipacaoParcelaProprietario === \'Aprovado\',' +
                            '\'label-danger\': row.entity.statusAntecipacaoParcelaProprietario === \'Erro\'' +
                        '}">{{row.entity.statusAntecipacaoDescricao}}</span>' +
                        '</div>',
                    width: 150
                },
                { 
                    name: 'valor', 
                    displayName: 'Valor',
                    cellFilter: 'currency:"R$ "',
                    width: 120
                },
                { name: 'Descricao', displayName: 'Descrição', width: 200 },
                { name: 'antecipacaoMotivo', displayName: 'Motivo Pendência ', width: 200 },
                { name: 'motivoPendencia', displayName: 'Motivo Antecipação ', width: 200 },
                { name: 'cpfCnpjProprietario', displayName: 'CPF/CNPJ Proprietário', width: 150 },
                { name: 'nomeProprietario', displayName: 'Nome Proprietário', width: 200 },
                { name: 'razaoSocialEmpresa', displayName: 'Razão Social Empresa', width: 200 },
                { name: 'cnpjEmpresa', displayName: 'CNPJ Empresa', width: 150 },
                { name: 'dataPrevisaoPagamento', displayName: 'Data Previsão Pagamento', width: 150, enableFiltering: true },
                { name: 'dataBaixa', displayName: 'Data Baixa Parcela', width: 150 },
                { name: 'dataCadastroAntecipacao', displayName: 'Data Cadastro Antecipação', width: 180, enableFiltering: true },
                { name: 'dataAlteracaoAntecipacao', displayName: 'Data Alteração Antecipação', width: 180, enableFiltering: true }
            ]
        };

        vm.toggleFiltros = function() {
            vm.filtrosExpandidos = !vm.filtrosExpandidos;
        };

        vm.fecharAlerta = function() {
            vm.alertaFechado = true;
            localStorage.setItem('centralPendenciasAlertaFechado', 'true');
        };

        vm.limparTotais = function() {
            vm.totalPagamentos = 0;
            vm.totalSemana = 0;
            vm.totalMes = 0;
            vm.totalPeriodoSelecionado = 0;
        };

        vm.formatarCpfCnpj = function(value) {
            if (!value) return '';
            var digits = value.replace(/\D/g, '');
            if (digits.length <= 11) {
                return digits.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
            } else {
                return digits.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
            }
        };

        vm.reexibirAlerta = function() {
            vm.alertaFechado = false;
            localStorage.removeItem('centralPendenciasAlertaFechado');
        };

        vm.consultar = function() {
            if (vm.gridOptions && vm.gridOptions.dataSource && vm.gridOptions.dataSource.refresh) {
                vm.gridOptions.dataSource.refresh();
            }
        };

        vm.visualizarDetalhes = function(pagamento) {
            $state.go('central-pendencias-pagamentos-antecipados.detalhes-pagamento', { 
                pagamentoId: pagamento.id,
                viagemId: pagamento.viagemId 
            });
        };



        vm.limparFiltros = function() {
            vm.filtros = {
                periodo: {
                    startDate: moment().subtract(7, 'days').startOf('day'),
                    endDate: moment().endOf('day')
                },
                empresaId: null,
                cpfCnpjProprietario: ''
            };
            vm.consultar();
        };
    }
})();
