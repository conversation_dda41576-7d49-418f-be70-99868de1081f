<style>
    textarea:hover {
        cursor: pointer;
    }
</style>
<div id="TransacoesPagamentoController" ng-controller="TransacoesPagamentoController as vm">
    <form-header items="vm.headerItems" head="'Detalhes pagamento'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Painel de pagamentos Viagem</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <div form-wizard steps="1" class="row">
                            <div class="form-wizard">
                                <ol class="row">
                                    <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1)"
                                        class="control-label col-xs-12 col-md-12" style="text-align: center;">
                                        <h4>Detalhes Pagamento</h4>
                                    </li>
                                </ol>
                                <div>
                                    <div ng-show="wizard.active(1)">
                                        <br>
                                        <div class="row">
                                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right; padding-top: 10px;">Código do
                                                        Pagamento:</label>
                                                    </label>
                                                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                                        <input type="text" ng-disabled="true" ng-model="vm.codPagamento"
                                                            class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right; padding-top: 10px;">Data/Hora
                                                        Requisição:</label>
                                                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                                        <input type="text" ng-disabled="true"
                                                            ng-model="vm.logsPagamentoObj.dataCadastro"
                                                            class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right; padding-top: 10px;">Data/Hora
                                                        Resposta:</label>
                                                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                                        <input type="text" ng-disabled="true"
                                                            ng-model="vm.logsPagamentoObj.dataRetorno"
                                                            class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-xs-12 col-md-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right;">
                                                        Json envio:
                                                    </label>
                                                    <div class="input-group col-xs-12 col-md-9">
                                                        <a ng-click="vm.logsPagamentoObj.jsonEnvio ? vm.copiar(0) : null">
                                                            <textarea style="resize: none;" type="text"
                                                                ng-disabled="!vm.logsPagamentoObj.jsonEnvio"
                                                                tooltip-placement="left"
                                                                uib-tooltip="{{vm.logsPagamentoObj.jsonEnvio ? 'Copiar json envio' : ''}}"
                                                                validate-on="blur" readonly multiple maxlength="10000"
                                                                name="jsonEnvio" rows="10"
                                                                class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.logsPagamentoObj.jsonEnvio ? (vm.jsonUtils.prettyPrint(vm.logsPagamentoObj.jsonEnvio) || '') : '' }}</textarea>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-12 col-md-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right;">
                                                        Json resposta:
                                                    </label>
                                                    <div class="input-group col-xs-12 col-md-9">
                                                        <a ng-click="vm.logsPagamentoObj.jsonRetorno ? vm.copiar(1) : null">
                                                            <textarea style="resize: none;" type="text"
                                                                ng-disabled="!vm.logsPagamentoObj.jsonRetorno"
                                                                tooltip-placement="left"
                                                                uib-tooltip="{{vm.logsPagamentoObj.jsonRetorno ? 'Copiar json resposta' : ''}}"
                                                                validate-on="blur" readonly multiple maxlength="10000"
                                                                name="jsonRetorno" rows="10"
                                                                class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.logsPagamentoObj.jsonRetorno ? (vm.jsonUtils.prettyPrint(vm.logsPagamentoObj.jsonRetorno) || '') : '' }}</textarea>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" ng-if="vm.logsPagamentoObj.dataCadastroCancelamento && vm.logsPagamentoObj.dataRetornoCancelamento" style="padding-top: 25px;">
                                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right;">Data/Hora
                                                        Requisição Cancelamento:</label>
                                                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                                        <input type="text" ng-disabled="true"
                                                            ng-model="vm.logsPagamentoObj.dataCadastroCancelamento"
                                                            class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right;">Data/Hora
                                                        Resposta Cancelamento:</label>
                                                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                                        <input type="text" ng-disabled="true"
                                                            ng-model="vm.logsPagamentoObj.dataRetornoCancelamento"
                                                            class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" ng-if="vm.logsPagamentoObj.jsonEnvioCancelamento && vm.logsPagamentoObj.jsonRetornoCancelamento">
                                            <div class="col-xs-12 col-md-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right;">
                                                        Json envio cancelamento:
                                                    </label>
                                                    <div class="input-group col-xs-12 col-md-9">
                                                        <a ng-click="vm.logsPagamentoObj.jsonEnvioCancelamento ? vm.copiar(2) : null">
                                                            <textarea style="resize: none;" type="text"
                                                                ng-disabled="!vm.logsPagamentoObj.jsonEnvioCancelamento"
                                                                tooltip-placement="left"
                                                                uib-tooltip="{{vm.logsPagamentoObj.jsonEnvioCancelamento ? 'Copiar json envio do cancelamento' : ''}}"
                                                                validate-on="blur" readonly multiple maxlength="10000"
                                                                name="jsonEnvioCancelamento" rows="10"
                                                                class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.logsPagamentoObj.jsonEnvioCancelamento ? (vm.jsonUtils.prettyPrint(vm.logsPagamentoObj.jsonEnvioCancelamento) || '') : '' }}</textarea>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-12 col-md-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right;">
                                                        Json resposta cancelamento:
                                                    </label>
                                                    <div class="input-group col-xs-12 col-md-9">
                                                        <a ng-click="vm.logsPagamentoObj.jsonRetornoCancelamento ? vm.copiar(3) : null">
                                                            <textarea style="resize: none;" type="text"
                                                                ng-disabled="!vm.logsPagamentoObj.jsonRetornoCancelamento"
                                                                tooltip-placement="left"
                                                                uib-tooltip="{{vm.logsPagamentoObj.jsonRetornoCancelamento ? 'Copiar json resposta do cancelamento' : ''}}"
                                                                validate-on="blur" readonly multiple maxlength="10000"
                                                                name="jsonRetornoCancelamento" rows="10"
                                                                class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.logsPagamentoObj.jsonRetornoCancelamento ? (vm.jsonUtils.prettyPrint(vm.logsPagamentoObj.jsonRetornoCancelamento) || '') : '' }}</textarea>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <br>
                                        <hr-label dark="true" title="'Transações'"></hr-label>
                                        <br><br>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div ui-grid="vm.gridOptions"
                                                    ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;"
                                                    ui-grid-pinning ui-grid-save-state ui-grid-pagination
                                                    ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <hr />
                                <br />
                                <div class="row">
                                    <div class="form-group">
                                        <div class="col-md-12 col-lg-12 text-right">
                                            <button type="button" ng-click="vm.onClickVoltar(wizard)" class="btn btn-labeled btn-default">
                                                <span class="btn-label">
                                                    <i class="fa fa-arrow-circle-left"></i>
                                                </span>
                                                Voltar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>