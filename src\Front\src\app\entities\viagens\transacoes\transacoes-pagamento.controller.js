(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('TransacoesPagamentoController', TransacoesPagamentoController);

    TransacoesPagamentoController.inject = ['BaseService', '$rootScope', 'toastr', '$stateParams', '$scope', 'PersistentDataService', '$timeout', '$state', '$uibModal', 'PERFIL_ADMINISTRADOR'];

    function TransacoesPagamentoController(BaseService, $rootScope, toastr, $scope, $stateParams, PersistentDataService, $timeout, $state, $uibModal, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Painel de pagamento viagem',
            link: 'viagens.index',
        }, {
            name: 'Pagamentos',
            link: 'viagens.pagamentos-viagem({link:' + $stateParams.viagem + '})'
        }, {
            name: $stateParams.link == 'editar' ? 'Detalhes pagamento' : 'Detalhes pagamento'
        }];

        vm.onClickVoltar = function (wizard) {
            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('viagens.pagamentos-viagem', {
                    link: $stateParams.viagem,
                });

            wizard.go(ativoIndex - 1);
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.codPagamento = $stateParams.pagamento;
        vm.logsPagamentoObj = {};

        init();
        function init() {
            BaseService.post('Viagem', 'ConsultarTransacoesPagamento', {
                PagamentoId: vm.codPagamento
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    $uibModalInstance.close();
                    return;
                } else {
                    vm.logsPagamentoObj = response.data.logsPagamento;


                    var jsonEnvioObj = JSON.parse(vm.logsPagamentoObj.jsonEnvio);
                    var jsonEnvioCancelamentoObj = JSON.parse(vm.logsPagamentoObj.jsonEnvioCancelamento)

                    jsonEnvioObj.Tipo = getTipoPagamentoEventoDescricao(jsonEnvioObj.Tipo);
                    jsonEnvioObj.FormaPagamento = getFormaPagamentoEventoDescricao(jsonEnvioObj.FormaPagamento);
                    jsonEnvioObj.TipoConta = getTipoContaDescricao(jsonEnvioObj.TipoConta);
                    jsonEnvioCancelamentoObj.Evento = getEventoCancelamentoDescricao(jsonEnvioCancelamentoObj.Evento);
                    vm.logsPagamentoObj.jsonEnvio = JSON.stringify(jsonEnvioObj);
                    vm.logsPagamentoObj.jsonEnvioCancelamento = JSON.stringify(jsonEnvioCancelamentoObj);

                }
            });
        }

        vm.enumTipoPagamentoEvento = [
            { id: 0, descricao: 'Adiantamento' },
            { id: 1, descricao: 'Saldo' },
            { id: 2, descricao: 'Complemento' },
            { id: 3, descricao: 'Avulso' },
            { id: 4, descricao: 'Tarifa ANTT' },
            { id: 5, descricao: 'Cancelamento' },
            { id: 6, descricao: 'Tarifas' }
        ];

        vm.enumFormaPagamentoEvento = [
            { id: 1, descricao: 'Deposito' },
            { id: 4, descricao: 'Pix' },
            { id: 5, descricao: 'Antecipacao' },
            { id: 6, descricao: 'RetencaoAntecipacao' }
        ];

        vm.enumEventoCancelamento = [
            { id: 1, descricao: 'Cancelamento' },
            { id: 2, descricao: 'Estorno' }
        ];

        vm.enumTipoConta = [
            { id: 1, descricao: 'Corrente' },
            { id: 2, descricao: 'Poupanca' },
            { id: 3, descricao: 'Salario' }
        ];

        function getTipoPagamentoEventoDescricao(id) {
            for (var i = 0; i < vm.enumTipoPagamentoEvento.length; i++) {
                if (vm.enumTipoPagamentoEvento[i].id === id) {
                    return vm.enumTipoPagamentoEvento[i].descricao;
                }
            }
            return id;
        }


        function getEventoCancelamentoDescricao(id) {
            for (var i = 0; i < vm.enumEventoCancelamento.length; i++) {
                if (vm.enumEventoCancelamento[i].id === id) {
                    return vm.enumEventoCancelamento[i].descricao;
                }
            }
            return id;
        }

        function getFormaPagamentoEventoDescricao(id) {
            for (var i = 0; i < vm.enumFormaPagamentoEvento.length; i++) {
                if (vm.enumFormaPagamentoEvento[i].id === id) {
                    return vm.enumFormaPagamentoEvento[i].descricao;
                }
            }
            return id;
        }

        function getTipoContaDescricao(id) {
            for (var i = 0; i < vm.enumTipoConta.length; i++) {
                if (vm.enumTipoConta[i].id === id) {
                    return vm.enumTipoConta[i].descricao;
                }
            }
            return id;
        }

        vm.VerTransacao = function (idTransacao) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/viagens/detalhe-transacao/modal-detalhe-transacao-pagamento.html',
                controller: 'ModalDetalhesTransacaoPagamentoController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                windowClass: 'custom-modal-width',
                resolve: {
                    idTransacao: function () {
                        return idTransacao;
                    }
                }
            });
        };

        vm.prettyPrint = function (objJson) {
            if (!objJson) {
                return ''; // Retorna uma string vazia ou mensagem alternativa
            }
            try {
                var obj = JSON.parse(objJson);
                var pretty = JSON.stringify(obj, undefined, 4);
                return pretty;
            } catch (error) {
                console.error('Erro ao fazer o parsing do JSON:', error);
                return ''; // Em caso de erro, retorna uma string vazia ou mensagem alternativa
            }
        }

        vm.copiar = function (campo) {
            switch (campo) {
                case 0:
                    unsecuredCopyToClipboard(vm.logsPagamentoObj.jsonEnvio);
                    break;
                case 1:
                    unsecuredCopyToClipboard(vm.logsPagamentoObj.jsonRetorno);
                    break;
                case 2:
                    unsecuredCopyToClipboard(vm.logsPagamentoObj.jsonEnvioCancelamento);
                    break;
                case 3:
                    unsecuredCopyToClipboard(vm.logsPagamentoObj.jsonRetornoCancelamento);
                    break;
                default:
                    unsecuredCopyToClipboard(vm.logsPagamentoObj.jsonEnvio)
                    break;
            }
            toastr.success("Json copiado para área de transferência");
        }


        function unsecuredCopyToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
            } catch (err) {
                console.error('Unable to copy to clipboard', err);
            }
            document.body.removeChild(textArea);
        }

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Viagem/ConsultarTransacoesPagamento",
                params: function () {
                    return {
                        PagamentoId: vm.codPagamento
                    }
                }
            },
            columnDefs: [
                {
                    name: 'Ações',
                    width: '100',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                        <button tooltip-placement="right" uib-tooltip="Visualizar detalhes" type="button" \
                            ng-class="{ \'btn btn-xs btn-info\': true }" \
                            ng-click="grid.appScope.vm.VerTransacao(row.entity.transacaoId)">\
                            <i class="fa fa-eye"></i>\
                        </button>\
                    </div>'
                },
                {
                    name: 'Codigo',
                    displayName: 'Código',
                    width: '*',
                    minWidth: 120,
                    field: 'transacaoId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: '*',
                    minWidth: 120,
                    field: 'status',
                    serverField: 'Status',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EStatusPagamentoEvento'
                },
                {
                    name: 'FormaPagamento',
                    displayName: 'Forma Pagamento',
                    width: '*',
                    minWidth: 150,
                    field: 'formaPagamento',
                    serverField: 'FormaPagamento',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EFormaPagamentoEvento'
                },
                {
                    name: 'Tipo',
                    displayName: 'Tipo',
                    width: 150,
                    field: 'tipo',
                    serverField: 'tipo',
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'ETipoEvento',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.tipo === 0"> Adiantamento </p>\
                                        <p ng-show="row.entity.tipo === 1"> Saldo </p>\
                                        <p ng-show="row.entity.tipo === 2"> Complemento </p>\
                                        <p ng-show="row.entity.tipo === 3"> Avulso </p>\
                                        <p ng-show="row.entity.tipo === 4"> Tarifa ANTT </p>\
                                        <p ng-show="row.entity.tipo === 5"> Cancelamento </p>\
                                        <p ng-show="row.entity.tipo === 6"> Tarifas </p>\
                                   </div>'
                },
                {
                    name: 'Valor',
                    displayName: 'Valor',
                    width: '*',
                    minWidth: 120,
                    field: 'valor',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'CodigoContaOrigem',
                    displayName: 'Id Conta Origem',
                    width: '*',
                    minWidth: 200,
                    field: 'codigoContaOrigem',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'CodigoContaDestino',
                    displayName: 'Id Conta Destino',
                    width: '*',
                    minWidth: 200,
                    field: 'codigoContaDestino',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'ChavePix',
                    displayName: 'Chave Pix',
                    width: '*',
                    minWidth: 220,
                    field: 'chavePix',
                    type: 'text',
                    enableFiltering: false
                },
                {
                    name: 'CodigoBanco',
                    displayName: 'Código Banco',
                    width: '*',
                    minWidth: 120,
                    field: 'codigoBanco',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Agencia',
                    displayName: 'Agência',
                    width: '*',
                    minWidth: 150,
                    field: 'codigoBanco',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Conta',
                    displayName: 'Conta',
                    width: '*',
                    minWidth: 150,
                    field: 'conta',
                    type: 'text',
                    enableFiltering: true
                },
                {
                    name: 'TipoConta',
                    displayName: 'Tipo Conta',
                    width: '*',
                    minWidth: 150,
                    field: 'tipoConta',
                    type: 'text',
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'ETipoContaDock',
                    pipe: function (input) {
                        return tipoContaDock(input)
                    },
                    cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                            <p ng-show="row.entity.tipoConta === 1"> Corrente </p>\
                                            <p ng-show="row.entity.tipoConta === 2"> Poupança </p>\
                                            <p ng-show="row.entity.tipoConta === 3"> Salário </p>\
                                       </div>'
                },
                {
                    name: 'DataBaixa',
                    displayName: 'Data Baixa',
                    minWidth: 150,
                    field: 'dataBaixa',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data Cadastro',
                    minWidth: 150,
                    field: 'dataCadastro',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataAlteracao',
                    displayName: 'Data Alteração',
                    minWidth: 150,
                    field: 'dataAlteracao',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'Data Cancelamento',
                    displayName: 'Data Cancelamento',
                    minWidth: 150,
                    field: 'dataCancelamento',
                    type: 'date',
                    enableFiltering: false
                }
            ]
        };

        var selfScope = PersistentDataService.get('PagamentosViagemController');
        var filho = PersistentDataService.get('TransacoesPagamentoController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('viagens.pagamentos.transacoes.transacoes-pagamento', {
                    link: filho.data.transacao.id > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }


    }
})();