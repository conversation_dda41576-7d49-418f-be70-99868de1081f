(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('TransacoesPagamentoController', TransacoesPagamentoController);

    TransacoesPagamentoController.inject = ['BaseService', '$rootScope', 'toastr', '$stateParams', '$scope', 'PersistentDataService', '$timeout', '$state', '$uibModal', 'PERFIL_ADMINISTRADOR', 'JsonUtilsService'];

    function TransacoesPagamentoController(BaseService, $rootScope, toastr, $scope, $stateParams, PersistentDataService, $timeout, $state, $uibModal, PERFIL_ADMINISTRADOR, JsonUtilsService) {
        var vm = this;
        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Painel de pagamento viagem',
            link: 'viagens.index',
        }, {
            name: 'Pagamentos',
            link: 'viagens.pagamentos-viagem({link:' + $stateParams.viagem + '})'
        }, {
            name: $stateParams.link == 'editar' ? 'Detalhes pagamento' : 'Detalhes pagamento'
        }];

        vm.onClickVoltar = function (wizard) {
            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('viagens.pagamentos-viagem', {
                    link: $stateParams.viagem,
                });

            wizard.go(ativoIndex - 1);
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.codPagamento = $stateParams.pagamento;
        vm.logsPagamentoObj = {};

        init();
        function init() {
            BaseService.post('Viagem', 'ConsultarTransacoesPagamento', {
                PagamentoId: vm.codPagamento
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    $uibModalInstance.close();
                    return;
                } else {
                    vm.logsPagamentoObj = response.data.logsPagamento;

                    // Aplicar conversões de enum nos JSONs usando o serviço compartilhado
                    if (vm.logsPagamentoObj.jsonEnvio) {
                        var conversions = {
                            'Tipo': 'tipoPagamentoEvento',
                            'FormaPagamento': 'formaPagamentoEvento',
                            'TipoConta': 'tipoConta'
                        };
                        vm.logsPagamentoObj.jsonEnvio = JsonUtilsService.convertEnumsInJson(vm.logsPagamentoObj.jsonEnvio, conversions);
                    }

                    if (vm.logsPagamentoObj.jsonEnvioCancelamento) {
                        var cancelamentConversions = {
                            'Evento': 'eventoCancelamento'
                        };
                        vm.logsPagamentoObj.jsonEnvioCancelamento = JsonUtilsService.convertEnumsInJson(vm.logsPagamentoObj.jsonEnvioCancelamento, cancelamentConversions);
                    }

                }
            });
        }

        // Enums e funções de conversão movidos para JsonUtilsService

        vm.VerTransacao = function (idTransacao) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/viagens/detalhe-transacao/modal-detalhe-transacao-pagamento.html',
                controller: 'ModalDetalhesTransacaoPagamentoController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                windowClass: 'custom-modal-width',
                resolve: {
                    idTransacao: function () {
                        return idTransacao;
                    }
                }
            });
        };

        // Usar o serviço compartilhado para funções JSON
        vm.jsonUtils = JsonUtilsService;

        vm.copiar = function (campo) {
            var jsonToCopy;
            var label;

            switch (campo) {
                case 0:
                    jsonToCopy = vm.logsPagamentoObj.jsonEnvio;
                    label = 'JSON de envio';
                    break;
                case 1:
                    jsonToCopy = vm.logsPagamentoObj.jsonRetorno;
                    label = 'JSON de resposta';
                    break;
                case 2:
                    jsonToCopy = vm.logsPagamentoObj.jsonEnvioCancelamento;
                    label = 'JSON de envio do cancelamento';
                    break;
                case 3:
                    jsonToCopy = vm.logsPagamentoObj.jsonRetornoCancelamento;
                    label = 'JSON de resposta do cancelamento';
                    break;
                default:
                    jsonToCopy = vm.logsPagamentoObj.jsonEnvio;
                    label = 'JSON de envio';
                    break;
            }

            JsonUtilsService.copyToClipboard(jsonToCopy, label + ' copiado para área de transferência');
        }

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Viagem/ConsultarTransacoesPagamento",
                params: function () {
                    return {
                        PagamentoId: vm.codPagamento
                    }
                }
            },
            columnDefs: [
                {
                    name: 'Ações',
                    width: '100',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                        <button tooltip-placement="right" uib-tooltip="Visualizar detalhes" type="button" \
                            ng-class="{ \'btn btn-xs btn-info\': true }" \
                            ng-click="grid.appScope.vm.VerTransacao(row.entity.transacaoId)">\
                            <i class="fa fa-eye"></i>\
                        </button>\
                    </div>'
                },
                {
                    name: 'Codigo',
                    displayName: 'Código',
                    width: '*',
                    minWidth: 120,
                    field: 'transacaoId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: '*',
                    minWidth: 120,
                    field: 'status',
                    serverField: 'Status',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EStatusPagamentoEvento'
                },
                {
                    name: 'FormaPagamento',
                    displayName: 'Forma Pagamento',
                    width: '*',
                    minWidth: 150,
                    field: 'formaPagamento',
                    serverField: 'FormaPagamento',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EFormaPagamentoEvento'
                },
                {
                    name: 'Tipo',
                    displayName: 'Tipo',
                    width: 150,
                    field: 'tipo',
                    serverField: 'tipo',
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'ETipoEvento',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.tipo === 0"> Adiantamento </p>\
                                        <p ng-show="row.entity.tipo === 1"> Saldo </p>\
                                        <p ng-show="row.entity.tipo === 2"> Complemento </p>\
                                        <p ng-show="row.entity.tipo === 3"> Avulso </p>\
                                        <p ng-show="row.entity.tipo === 4"> Tarifa ANTT </p>\
                                        <p ng-show="row.entity.tipo === 5"> Cancelamento </p>\
                                        <p ng-show="row.entity.tipo === 6"> Tarifas </p>\
                                   </div>'
                },
                {
                    name: 'Valor',
                    displayName: 'Valor',
                    width: '*',
                    minWidth: 120,
                    field: 'valor',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'CodigoContaOrigem',
                    displayName: 'Id Conta Origem',
                    width: '*',
                    minWidth: 200,
                    field: 'codigoContaOrigem',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'CodigoContaDestino',
                    displayName: 'Id Conta Destino',
                    width: '*',
                    minWidth: 200,
                    field: 'codigoContaDestino',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'ChavePix',
                    displayName: 'Chave Pix',
                    width: '*',
                    minWidth: 220,
                    field: 'chavePix',
                    type: 'text',
                    enableFiltering: false
                },
                {
                    name: 'CodigoBanco',
                    displayName: 'Código Banco',
                    width: '*',
                    minWidth: 120,
                    field: 'codigoBanco',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Agencia',
                    displayName: 'Agência',
                    width: '*',
                    minWidth: 150,
                    field: 'codigoBanco',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Conta',
                    displayName: 'Conta',
                    width: '*',
                    minWidth: 150,
                    field: 'conta',
                    type: 'text',
                    enableFiltering: true
                },
                {
                    name: 'TipoConta',
                    displayName: 'Tipo Conta',
                    width: '*',
                    minWidth: 150,
                    field: 'tipoConta',
                    type: 'text',
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'ETipoContaDock',
                    pipe: function (input) {
                        return tipoContaDock(input)
                    },
                    cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                            <p ng-show="row.entity.tipoConta === 1"> Corrente </p>\
                                            <p ng-show="row.entity.tipoConta === 2"> Poupança </p>\
                                            <p ng-show="row.entity.tipoConta === 3"> Salário </p>\
                                       </div>'
                },
                {
                    name: 'DataBaixa',
                    displayName: 'Data Baixa',
                    minWidth: 150,
                    field: 'dataBaixa',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data Cadastro',
                    minWidth: 150,
                    field: 'dataCadastro',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataAlteracao',
                    displayName: 'Data Alteração',
                    minWidth: 150,
                    field: 'dataAlteracao',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'Data Cancelamento',
                    displayName: 'Data Cancelamento',
                    minWidth: 150,
                    field: 'dataCancelamento',
                    type: 'date',
                    enableFiltering: false
                }
            ]
        };

        var selfScope = PersistentDataService.get('PagamentosViagemController');
        var filho = PersistentDataService.get('TransacoesPagamentoController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('viagens.pagamentos.transacoes.transacoes-pagamento', {
                    link: filho.data.transacao.id > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }


    }
})();