(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PagamentosViagemController', PagamentosViagemController)
        .filter('placaMask', function() {
            return function(placa) {
                if (!placa) return '';

                var placaLimpa = placa.toString().replace(/\s/g, '').toUpperCase();

                if (placaLimpa.length === 7) {
                    if (/^[A-Z]{3}[0-9]{4}$/.test(placaLimpa)) {
                        return placaLimpa.substring(0, 3) + '-' + placaLimpa.substring(3);
                    }
                    else if (/^[A-Z]{3}[0-9][A-Z][0-9]{2}$/.test(placaLimpa)) {
                        return placaLimpa.substring(0, 3) + placaLimpa.substring(3, 4) +
                               placaLimpa.substring(4, 5) + placaLimpa.substring(5);
                    }
                }

                return placa;
            };
        });

        PagamentosViagemController.inject = ['BaseService', '$rootScope', 'toastr','$stateParams', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function PagamentosViagemController(BaseService, $rootScope, toastr, $scope, $stateParams, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Painel de pagamento viagem', 
            link: 'viagens.index'
        }, {
             name: $stateParams.link == 'editar' ? 'Pagamentos' : 'Pagamentos'
        }];

        vm.listaPagamentoHistoricoOptions = [];
        vm.logsViagem = [];
        vm.valoresViagem = [];
        vm.veiculosViagem = [];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.onClickVoltar = function (wizard) {
            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('viagens.index');

            wizard.go(ativoIndex - 1);
        };

        function init(){
            carregaHistoricoPagamentos();
            carregaLogsViagem();
            carregaValoresViagem();
            carregaVeiculosViagem();
        }

        vm.prettyPrint = function (objJson) {
            if (!objJson) {
                return ''; // Retorna uma string vazia ou mensagem alternativa
            }
            try {
                var obj = JSON.parse(objJson);
                var pretty = JSON.stringify(obj, undefined, 4);
                return pretty;
            } catch (error) {
                console.error('Erro ao fazer o parsing do JSON:', error);
                return ''; // Em caso de erro, retorna uma string vazia ou mensagem alternativa
            }
        }

        vm.codViagem = $stateParams.link;

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Viagem/ConsultarPagamentosViagem",
                params: function () {
                    return {
                        ViagemId: $stateParams.link
                    }
                }
            },
            columnDefs: [
                {
                    name: 'Ações',
                    width: '100',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                        <button tooltip-placement="right" uib-tooltip="Visualizar detalhes" type="button" \
                                ui-sref="viagens.transacoes-pagamento({viagem: grid.appScope.vm.codViagem , pagamento:row.entity.id})"\
                                ng-class="{ \'btn btn-xs btn-info\': true }">\
                                <i class="fa fa-eye"></i>\
                        </button>\
                    </div>'
                },
                {
                    name: 'Codigo',
                    displayName: 'Código',
                    width: '*',
                    minWidth: 120,
                    field: 'id',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'PagamentoExternoId',
                    displayName: 'Pagamento Externo Id',
                    width: '*',
                    minWidth: 200,
                    field: 'pagamentoExternoId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: '*',
                    minWidth: 120,
                    field: 'status',
                    serverField: 'Status',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EStatusPagamentoEvento'
                },
                {
                    name: 'FormaPagamento',
                    displayName: 'Forma Pagamento',
                    width: '*',
                    minWidth: 150,
                    field: 'formaPagamento',
                    serverField: 'FormaPagamento',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EFormaPagamentoEvento'
                },
                {
                    name: 'Tipo',
                    displayName: 'Tipo de Pagamento',
                    width: '*',
                    minWidth: 150,
                    field: 'tipo',
                    serverField: 'Tipo',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'ETipoEvento'
                },
                {
                    name: 'ValorParcela',
                    displayName: 'Valor Parcela',
                    width: '*',
                    minWidth: 120,
                    field: 'valorParcela',
                    enableFiltering: true
                },
                {
                    name: 'ValorTransferenciaMotorista',
                    displayName: 'Valor Transferência Motorista',
                    width: '*',
                    minWidth: 200,
                    field: 'valorTransferenciaMotorista',
                    enableFiltering: true
                },
                {
                    name: 'ValorTarifaBbc',
                    displayName: 'Valor Tarifa BBC',
                    width: '*',
                    minWidth: 120,
                    field: 'valorTarifaBbc',
                    enableFiltering: true
                },
                {
                    name: 'ValorTarifaPix',
                    displayName: 'Valor Tarifa Pix',
                    width: '*',
                    minWidth: 120,
                    field: 'valorTarifaPix',
                    enableFiltering: true
                },
                {
                    name: 'Mensagem',
                    displayName: 'Mensagem',
                    width: '*',
                    minWidth: 210,
                    field: 'mensagem',
                    type: 'text',
                    enableFiltering: true
                },
                {
                    name: 'ContadorReenvio',
                    displayName: 'Contador Reenvio',
                    width: '*',
                    minWidth: 150,
                    field: 'contadorReenvio',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'ContadorVerificacaoStatusPix',
                    displayName: 'Contador Verificação Status Pix',
                    width: '*',
                    minWidth: 200,
                    field: 'contadorVerificacaoStatusPix',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data Cadastro',
                    minWidth: 150,
                    field: 'dataCadastro',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataAlteracao',
                    displayName: 'Data Alteração',
                    width: '*',
                    minWidth: 150,
                    field: 'dataAlteracao',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataBaixa',
                    displayName: 'Data Baixa',
                    minWidth: 150,
                    field: 'dataBaixa',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataCancelamento',
                    displayName: 'Data Cancelamento',
                    minWidth: 150,
                    field: 'dataCancelamento',
                    type: 'date',
                    enableFiltering: false
                },
                 {
                    name: 'DataPrevisaoPagamento',
                    displayName: 'Data Previsão de Pagamento',
                    minWidth: 150,
                    field: 'dataPrevisaoPagamento',
                    type: 'date',
                    enableFiltering: false
                }
            ]
        };

     
        function carregaHistoricoPagamentos(codViagem) {
            BaseService.get("Viagem", "ConsultarListaPagamentosHistoricoViagem", {
                ViagemId: codViagem
            }).then(function (response) {
                if (response.success) {
                    vm.listaPagamentoHistoricoOptions = response.data;
                }
            });
        };

         function carregaLogsViagem() {
            BaseService.get("Viagem", "ConsultarLogsViagem", {
                idViagem: $stateParams.link
            }).then(function (response) {
                if (response.success) {
                    vm.logsViagem = response.data;
                    console.log('Logs carregados:', vm.logsViagem);
                } else {
                    console.error('Erro ao carregar logs:', response.error);
                    vm.logsViagem = [];
                }
            }).catch(function (error) {
                console.error('Erro na requisição de logs:', error);
                vm.logsViagem = [];
            });
        };


        function carregaValoresViagem() {
            BaseService.get("Viagem", "ConsultarValoresViagem", {
                idViagem: $stateParams.link
            }).then(function (response) {
                if (response.success) {
                    vm.valoresViagem = response.data;
                    console.log('Valores carregados:', vm.valoresViagem);
                } else {
                    console.error('Erro ao carregar valores:', response.error);
                    vm.valoresViagem = null;
                }
            }).catch(function (error) {
                console.error('Erro na requisição de valores:', error);
                vm.valoresViagem = null;
            });
        };

        function carregaVeiculosViagem() {
            BaseService.get("Viagem", "ConsultarVeiculosViagem", {
                idViagem: $stateParams.link
            }).then(function (response) {
                if (response.success) {
                    vm.veiculosViagem = response.data;
                    console.log('Veículos carregados:', vm.veiculosViagem);
                } else {
                    console.error('Erro ao carregar veículos:', response.error);
                    vm.veiculosViagem = [];
                }
            }).catch(function (error) {
                console.error('Erro na requisição de veículos:', error);
                vm.veiculosViagem = [];
            });
        };

        vm.gridPagamentoHistoricoOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridPagamentoHistoricoOptions"),
            dataSource: {
                url: "Viagem/ConsultarPagamentosHistoricoViagem",
                params: function () {
                    return {
                        ViagemId: $stateParams.link
                    }  
                }
            },
            columnDefs: [
                {
                    name: 'Ações',
                    width: '100',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                        <button tooltip-placement="right" uib-tooltip="Visualizar detalhes" type="button" \
                                ui-sref="viagens.transacoes-pagamento-historico({viagem: grid.appScope.vm.codViagem , pagamentoHistorico:row.entity.id , pagamentoEvento: row.entity.pagamentoEventoId})"\
                                ng-class="{ \'btn btn-xs btn-info\': true }">\
                                <i class="fa fa-eye"></i>\
                        </button>\
                    </div>'
                },
                {
                    name: 'Codigo',
                    displayName: 'Código',
                    width: '*',
                    minWidth: 120,
                    field: 'id',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Pagamento Evento Id',
                    displayName: 'Pagamento Evento Id',
                    width: '*',
                    minWidth: 120,
                    field: 'pagamentoEventoId',
                    type: 'number',
                    hide: true,
                    enableFiltering: true
                },
                {
                    name: 'PagamentoExternoId',
                    displayName: 'Pagamento Externo Id',
                    width: '*',
                    minWidth: 200,
                    field: 'pagamentoExternoId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: '*',
                    minWidth: 120,
                    field: 'status',
                    serverField: 'Status',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EStatusPagamentoEvento',
                },
                {
                    name: 'FormaPagamento',
                    displayName: 'Forma Pagamento',
                    width: '*',
                    minWidth: 150,
                    field: 'formaPagamento',
                    serverField: 'FormaPagamento',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EFormaPagamentoEvento'
                },
                {
                    name: 'Tipo',
                    displayName: 'Tipo de Pagamento',
                    width: '*',
                    minWidth: 150,
                    field: 'tipo',
                    serverField: 'Tipo',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'ETipoEvento'
                },
                {
                    name: 'ValorParcela',
                    displayName: 'Valor Parcela',
                    width: '*',
                    minWidth: 120,
                    field: 'valorParcela',
                    enableFiltering: true
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data Cadastro',
                    minWidth: 150,
                    field: 'dataCadastro',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataAlteracao',
                    displayName: 'Data Alteração',
                    width: '*',
                    minWidth: 150,
                    field: 'dataAlteracao',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataBaixa',
                    displayName: 'Data Baixa',
                    minWidth: 150,
                    field: 'dataBaixa',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'Data Cancelamento',
                    displayName: 'Data Cancelamento',
                    minWidth: 150,
                    field: 'dataCancelamento',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'ValorTransferenciaMotorista',
                    displayName: 'Valor Transferência Motorista',
                    width: '*',
                    minWidth: 200,
                    field: 'valorTransferenciaMotorista',
                    enableFiltering: true
                },
                {
                    name: 'ValorTarifaBbc',
                    displayName: 'Valor Tarifa BBC',
                    width: '*',
                    minWidth: 120,
                    field: 'valorTarifaBbc',
                    enableFiltering: true
                },
                {
                    name: 'ValorTarifaPix',
                    displayName: 'Valor Tarifa Pix',
                    width: '*',
                    minWidth: 120,
                    field: 'valorTarifaPix',
                    enableFiltering: true
                },
                {
                    name: 'Mensagem',
                    displayName: 'Mensagem',
                    width: '*',
                    minWidth: 210,
                    field: 'mensagem',
                    type: 'text',
                    enableFiltering: true
                },
                {
                    name: 'ContadorReenvio',
                    displayName: 'Contador Reenvio',
                    width: '*',
                    minWidth: 150,
                    field: 'contadorReenvio',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'ContadorVerificacaoStatusPix',
                    displayName: 'Contador Verificação Status Pix',
                    width: '*',
                    minWidth: 200,
                    field: 'contadorVerificacaoStatusPix',
                    type: 'number',
                    enableFiltering: true
                }
            ]
        };

        init();

    }
})();