(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .factory('JsonUtilsService', JsonUtilsService);

    JsonUtilsService.$inject = ['toastr'];

    function JsonUtilsService(toastr) {
        // Definições dos enums
        var enums = {
            tipoPagamentoEvento: [
                { id: 0, descricao: 'Adiantamento' },
                { id: 1, descricao: 'Saldo' },
                { id: 2, descricao: 'Complemento' },
                { id: 3, descricao: 'Avulso' },
                { id: 4, descricao: 'Tarifa ANTT' },
                { id: 5, descricao: 'Cancelamento' },
                { id: 6, descricao: 'Tarifas' }
            ],
            formaPagamentoEvento: [
                { id: 1, descricao: 'Deposito' },
                { id: 4, descricao: 'Pix' },
                { id: 5, descricao: 'Antecipacao' },
                { id: 6, descricao: 'RetencaoAntecipacao' }
            ],
            eventoCancelamento: [
                { id: 1, descricao: 'Cancelamento' },
                { id: 2, descricao: 'Estorno' }
            ],
            tipoConta: [
                { id: 1, descricao: 'Corrente' },
                { id: 2, descricao: 'Poupanca' },
                { id: 3, descricao: 'Salario' }
            ]
        };
        var service = {
            prettyPrint: prettyPrint,
            copyToClipboard: copyToClipboard,
            copyJsonByIndex: copyJsonByIndex,
            convertEnumsInJson: convertEnumsInJson,
            getEnumDescription: getEnumDescription
        };

        return service;

        /**
         * Formata um JSON string para exibição com indentação
         * @param {string} objJson - String JSON para formatar
         * @returns {string} - JSON formatado ou string vazia em caso de erro
         */
        function prettyPrint(objJson) {
            if (!objJson) {
                return ''; // Retorna uma string vazia se não há JSON
            }
            try {
                var obj = JSON.parse(objJson);
                var pretty = JSON.stringify(obj, undefined, 4);
                return pretty;
            } catch (error) {
                console.error('Erro ao fazer o parsing do JSON:', error);
                return objJson; // Retorna o JSON original se não conseguir fazer parse
            }
        }

        /**
         * Copia texto para a área de transferência
         * @param {string} text - Texto para copiar
         * @param {string} successMessage - Mensagem de sucesso personalizada (opcional)
         */
        function copyToClipboard(text, successMessage) {
            if (!text) {
                toastr.warning('Não há conteúdo para copiar');
                return;
            }

            try {
                var textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                var successful = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (successful) {
                    toastr.success(successMessage || 'JSON copiado para área de transferência');
                } else {
                    throw new Error('Comando de cópia falhou');
                }
            } catch (err) {
                console.error('Erro ao copiar para área de transferência:', err);
                toastr.error('Erro ao copiar conteúdo');
            }
        }

        /**
         * Copia JSON baseado em um índice e array de JSONs
         * @param {number} index - Índice do JSON a ser copiado
         * @param {Array} jsonArray - Array com os JSONs
         * @param {Array} labels - Array com os labels dos JSONs (opcional)
         */
        function copyJsonByIndex(index, jsonArray, labels) {
            if (!jsonArray || index < 0 || index >= jsonArray.length) {
                toastr.warning('JSON não encontrado');
                return;
            }

            var jsonToCopy = jsonArray[index];
            var label = labels && labels[index] ? labels[index] : 'JSON';

            copyToClipboard(jsonToCopy, label + ' copiado para área de transferência');
        }

        /**
         * Obtém a descrição de um enum baseado no ID
         * @param {string} enumType - Tipo do enum (tipoPagamentoEvento, formaPagamentoEvento, etc.)
         * @param {number} id - ID do enum
         * @returns {string} - Descrição do enum ou o ID original se não encontrado
         */
        function getEnumDescription(enumType, id) {
            console.log('getEnumDescription chamado com:', { enumType: enumType, id: id });
            console.log('Enums disponíveis:', Object.keys(enums));

            var enumArray = enums[enumType];
            if (!enumArray) {
                console.log('Enum não encontrado:', enumType);
                return id;
            }

            console.log('Array do enum encontrado:', enumArray);
            for (var i = 0; i < enumArray.length; i++) {
                if (enumArray[i].id === id) {
                    console.log('Match encontrado:', enumArray[i]);
                    return enumArray[i].descricao;
                }
            }
            console.log('Nenhum match encontrado para ID:', id);
            return id;
        }

        /**
         * Converte enums em um JSON string, substituindo IDs por descrições
         * @param {string} jsonString - String JSON para converter
         * @param {Object} conversions - Objeto com as conversões a serem feitas
         * @returns {string} - JSON string com enums convertidos
         */
        function convertEnumsInJson(jsonString, conversions) {
            console.log('convertEnumsInJson chamado com:', { jsonString: jsonString, conversions: conversions });

            if (!jsonString || !conversions) {
                console.log('Retornando sem conversão - jsonString ou conversions vazios');
                return jsonString;
            }

            try {
                var jsonObj = JSON.parse(jsonString);
                console.log('JSON parseado:', jsonObj);

                // Aplica as conversões especificadas
                for (var property in conversions) {
                    if (jsonObj.hasOwnProperty(property) && conversions.hasOwnProperty(property)) {
                        var enumType = conversions[property];
                        var originalValue = jsonObj[property];
                        var convertedValue = getEnumDescription(enumType, originalValue);
                        console.log('Convertendo propriedade:', property, 'de', originalValue, 'para', convertedValue, 'usando enum', enumType);
                        jsonObj[property] = convertedValue;
                    } else {
                        console.log('Propriedade não encontrada no JSON:', property);
                    }
                }

                var result = JSON.stringify(jsonObj);
                console.log('JSON final convertido:', result);
                return result;
            } catch (error) {
                console.error('Erro ao converter enums no JSON:', error);
                return jsonString;
            }
        }
    }
})();
