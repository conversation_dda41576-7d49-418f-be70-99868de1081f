(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .factory('JsonUtilsService', JsonUtilsService);

    JsonUtilsService.$inject = ['toastr'];

    function JsonUtilsService(toastr) {
        var service = {
            prettyPrint: prettyPrint,
            copyToClipboard: copyToClipboard,
            copyJsonByIndex: copyJsonByIndex
        };

        return service;

        /**
         * Formata um JSON string para exibição com indentação
         * @param {string} objJson - String JSON para formatar
         * @returns {string} - JSON formatado ou string vazia em caso de erro
         */
        function prettyPrint(objJson) {
            if (!objJson) {
                return ''; // Retorna uma string vazia se não há JSON
            }
            try {
                var obj = JSON.parse(objJson);
                var pretty = JSON.stringify(obj, undefined, 4);
                return pretty;
            } catch (error) {
                console.error('Erro ao fazer o parsing do JSON:', error);
                return objJson; // Retorna o JSON original se não conseguir fazer parse
            }
        }

        /**
         * Copia texto para a área de transferência
         * @param {string} text - Texto para copiar
         * @param {string} successMessage - Mensagem de sucesso personalizada (opcional)
         */
        function copyToClipboard(text, successMessage) {
            if (!text) {
                toastr.warning('Não há conteúdo para copiar');
                return;
            }

            try {
                var textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                var successful = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (successful) {
                    toastr.success(successMessage || 'JSON copiado para área de transferência');
                } else {
                    throw new Error('Comando de cópia falhou');
                }
            } catch (err) {
                console.error('Erro ao copiar para área de transferência:', err);
                toastr.error('Erro ao copiar conteúdo');
            }
        }

        /**
         * Copia JSON baseado em um índice e array de JSONs
         * @param {number} index - Índice do JSON a ser copiado
         * @param {Array} jsonArray - Array com os JSONs
         * @param {Array} labels - Array com os labels dos JSONs (opcional)
         */
        function copyJsonByIndex(index, jsonArray, labels) {
            if (!jsonArray || index < 0 || index >= jsonArray.length) {
                toastr.warning('JSON não encontrado');
                return;
            }

            var jsonToCopy = jsonArray[index];
            var label = labels && labels[index] ? labels[index] : 'JSON';
            
            copyToClipboard(jsonToCopy, label + ' copiado para área de transferência');
        }
    }
})();
