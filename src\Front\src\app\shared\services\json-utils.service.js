(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .factory('JsonUtilsService', JsonUtilsService);

    JsonUtilsService.$inject = ['toastr'];

    function JsonUtilsService(toastr) {
        // Definições dos enums
        var enums = {
            tipoPagamentoEvento: [
                { id: 0, descricao: 'Adiantamento' },
                { id: 1, descricao: 'Saldo' },
                { id: 2, descricao: 'Complemento' },
                { id: 3, descricao: 'Avulso' },
                { id: 4, descricao: 'Tarifa ANTT' },
                { id: 5, descricao: 'Cancelamento' },
                { id: 6, descricao: 'Tarifas' }
            ],
            formaPagamentoEvento: [
                { id: 0, descricao: 'Não Informado' },
                { id: 1, descricao: 'Depósito' },
                { id: 2, descricao: 'TED' },
                { id: 3, descricao: 'DOC' },
                { id: 4, descricao: 'Pix' },
                { id: 5, descricao: 'Antecipação' },
                { id: 6, descricao: 'Retenção Antecipação' }
            ],
            eventoCancelamento: [
                { id: 1, descricao: 'Cancelamento' },
                { id: 2, descricao: 'Estorno' }
            ],
            tipoConta: [
                { id: 0, descricao: 'Não Informado' },
                { id: 1, descricao: 'Corrente' },
                { id: 2, descricao: 'Poupança' },
                { id: 3, descricao: 'Salário' }
            ],
            status: [
                { id: 0, descricao: 'Fechado' },
                { id: 1, descricao: 'Aberto' },
                { id: 2, descricao: 'Pendente' },
                { id: 3, descricao: 'Erro' },
                { id: 4, descricao: 'Cancelado' },
                { id: 5, descricao: 'Processando' },
                { id: 6, descricao: 'Não Executado' },
            ],
            tipoViagem: [
                { id: 0, descricao: 'Não Informado' },
                { id: 1, descricao: 'Padrão' },
                { id: 3, descricao: 'TAC-Agregado' },
            ],
            statusViagem: [
                { id: 0, descricao: 'Bloqueado' },
                { id: 1, descricao: 'Aberto' },
                { id: 2, descricao: 'Baixado' },
                { id: 3, descricao: 'Cancelado' },
                { id: 4, descricao: 'Pendente' },
            ],
            statusCiot: [
                { id: 0, descricao: 'Não Informado' },
                { id: 1, descricao: 'Ciot Gerado' },
                { id: 2, descricao: 'Contingência' },
                { id: 3, descricao: 'Cancelado' },
                { id: 4, descricao: 'Encerrado' },
            ]
        };
        var service = {
            prettyPrint: prettyPrint,
            copyToClipboard: copyToClipboard,
            copyJsonByIndex: copyJsonByIndex,
            convertEnumsInJson: convertEnumsInJson,
            getEnumDescription: getEnumDescription
        };

        return service;

        /**
         * Formata um JSON string para exibição com indentação
         * @param {string} objJson - String JSON para formatar
         * @returns {string} - JSON formatado ou string vazia em caso de erro
         */
        function prettyPrint(objJson) {
            if (!objJson) {
                return ''; // Retorna uma string vazia se não há JSON
            }
            try {
                var obj = JSON.parse(objJson);
                var pretty = JSON.stringify(obj, undefined, 4);
                return pretty;
            } catch (error) {
                console.error('Erro ao fazer o parsing do JSON:', error);
                return objJson; // Retorna o JSON original se não conseguir fazer parse
            }
        }

        /**
         * Copia texto para a área de transferência
         * @param {string} text - Texto para copiar
         * @param {string} successMessage - Mensagem de sucesso personalizada (opcional)
         */
        function copyToClipboard(text, successMessage) {
            if (!text) {
                toastr.warning('Não há conteúdo para copiar');
                return;
            }

            try {
                var textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                var successful = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (successful) {
                    toastr.success(successMessage || 'JSON copiado para área de transferência');
                } else {
                    throw new Error('Comando de cópia falhou');
                }
            } catch (err) {
                console.error('Erro ao copiar para área de transferência:', err);
                toastr.error('Erro ao copiar conteúdo');
            }
        }

        /**
         * Copia JSON baseado em um índice e array de JSONs
         * @param {number} index - Índice do JSON a ser copiado
         * @param {Array} jsonArray - Array com os JSONs
         * @param {Array} labels - Array com os labels dos JSONs (opcional)
         */
        function copyJsonByIndex(index, jsonArray, labels) {
            if (!jsonArray || index < 0 || index >= jsonArray.length) {
                toastr.warning('JSON não encontrado');
                return;
            }

            var jsonToCopy = jsonArray[index];
            var label = labels && labels[index] ? labels[index] : 'JSON';

            copyToClipboard(jsonToCopy, label + ' copiado para área de transferência');
        }

        /**
         * Obtém a descrição de um enum baseado no ID
         * @param {string} enumType - Tipo do enum (tipoPagamentoEvento, formaPagamentoEvento, etc.)
         * @param {number} id - ID do enum
         * @returns {string} - Descrição do enum ou o ID original se não encontrado
         */
        function getEnumDescription(enumType, id) {
            var enumArray = enums[enumType];
            if (!enumArray) {
                return id;
            }

            for (var i = 0; i < enumArray.length; i++) {
                if (enumArray[i].id === id) {
                    return enumArray[i].descricao;
                }
            }
            return id;
        }

        /**
         * Converte enums em um JSON string, substituindo IDs por descrições
         * @param {string} jsonString - String JSON para converter
         * @param {Object} conversions - Objeto com as conversões a serem feitas
         * @returns {string} - JSON string com enums convertidos
         */
        function convertEnumsInJson(jsonString, conversions) {
            if (!jsonString || !conversions) {
                return jsonString;
            }

            try {
                var jsonObj = JSON.parse(jsonString);

                // Função recursiva para converter enums em objetos aninhados
                function convertObjectEnums(obj, conversions) {
                    if (Array.isArray(obj)) {
                        // Se for um array, aplica a conversão em cada item
                        for (var i = 0; i < obj.length; i++) {
                            obj[i] = convertObjectEnums(obj[i], conversions);
                        }
                    } else if (obj && typeof obj === 'object') {
                        // Se for um objeto, verifica cada propriedade
                        for (var property in conversions) {
                            if (obj.hasOwnProperty(property) && conversions.hasOwnProperty(property)) {
                                var enumType = conversions[property];
                                var originalValue = obj[property];
                                var convertedValue = getEnumDescription(enumType, originalValue);
                                obj[property] = convertedValue;
                            }
                        }

                        // Recursivamente converte objetos aninhados
                        for (var key in obj) {
                            if (obj.hasOwnProperty(key) && typeof obj[key] === 'object') {
                                obj[key] = convertObjectEnums(obj[key], conversions);
                            }
                        }
                    }
                    return obj;
                }

                // Aplica as conversões no objeto principal
                jsonObj = convertObjectEnums(jsonObj, conversions);

                return JSON.stringify(jsonObj);
            } catch (error) {
                console.error('Erro ao converter enums no JSON:', error);
                return jsonString;
            }
        }
    }
})();
