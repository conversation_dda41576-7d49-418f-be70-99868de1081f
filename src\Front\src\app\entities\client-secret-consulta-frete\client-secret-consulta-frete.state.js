(function () {
    'use strict';

    angular.module('bbcWeb.client-secret-consulta-frete.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('client-secret-consulta-frete', {
            abstract: true,
            url: "/client-secret-consulta-frete",
            templateUrl: "app/layout/content.html"
        }).state('client-secret-consulta-frete.index', {
            url: '/index',
            templateUrl: 'app/entities/client-secret-consulta-frete/client-secret-consulta-frete.html'
        }).state('client-secret-consulta-frete.client-secret-consulta-frete-crud', {
            url: '/:link',
            templateUrl: 'app/entities/client-secret-consulta-frete/client-secret-consulta-frete-crud.html'
        });
    }
})();
