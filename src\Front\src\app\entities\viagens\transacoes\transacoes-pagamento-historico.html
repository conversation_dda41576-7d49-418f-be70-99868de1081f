<style>
    textarea:hover {
        cursor: pointer;
    }
</style>
<div id="TransacoesPagamentoHistoricoController" ng-controller="TransacoesPagamentoHistoricoController as vm">
    <form-header items="vm.headerItems" head="'Detalhes pagamento'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Painel de pagamentos Viagem</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <div form-wizard steps="1" class="row">
                            <div class="form-wizard">
                                <ol class="row">
                                    <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1)"
                                        class="control-label col-xs-12 col-md-12" style="text-align: center;">
                                        <h4>Detalhes Pagamento Histórico</h4>
                                    </li>
                                </ol>
                                <div>
                                    <div ng-show="wizard.active(1)">
                                        <br>
                                        <div class="row">
                                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right; padding-top: 10px;">Código do Pagamento:</label>
                                                    </label>
                                                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                                        <input type="text" ng-disabled="true" ng-model="vm.codPagamentoEvento"
                                                            class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right; padding-top: 10px;">Código do Histórico do Pagamento:</label>
                                                    </label>
                                                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                                        <input type="text" ng-disabled="true" ng-model="vm.codPagamentoHistorico"
                                                            class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right; padding-top: 10px;">Data/Hora
                                                        Requisição:</label>
                                                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                                        <input type="text" ng-disabled="true"
                                                            ng-model="vm.logsPagamentoHistoricoObj.dataCadastro"
                                                            class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right; padding-top: 10px;">Data/Hora
                                                        Resposta:</label>
                                                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                                        <input type="text" ng-disabled="true"
                                                            ng-model="vm.logsPagamentoHistoricoObj.dataRetorno"
                                                            class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-xs-12 col-md-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right;">
                                                        Json envio:
                                                    </label>
                                                    <div class="input-group col-xs-12 col-md-9">
                                                        <a ng-click="vm.logsPagamentoHistoricoObj.jsonEnvio ? vm.copiar(1) : null">
                                                            <textarea style="resize: none;" type="text"
                                                                ng-disabled="!vm.logsPagamentoHistoricoObj.jsonEnvio"
                                                                tooltip-placement="left"
                                                                uib-tooltip="{{vm.logsPagamentoHistoricoObj.jsonEnvio ? 'Copiar json envio' : ''}}"
                                                                validate-on="blur" readonly multiple maxlength="10000"
                                                                name="jsonEnvio" rows="10"
                                                                class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.logsPagamentoHistoricoObj.jsonEnvio ? (vm.jsonUtils.prettyPrint(vm.logsPagamentoHistoricoObj.jsonEnvio) || '') : '' }}</textarea>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-12 col-md-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right;">
                                                        Json resposta:
                                                    </label>
                                                    <div class="input-group col-xs-12 col-md-9">
                                                        <a ng-click="vm.logsPagamentoHistoricoObj.jsonRetorno ? vm.copiar(2) : null">
                                                            <textarea style="resize: none;" type="text"
                                                                ng-disabled="!vm.logsPagamentoHistoricoObj.jsonRetorno"
                                                                tooltip-placement="left"
                                                                uib-tooltip="{{vm.logsPagamentoHistoricoObj.jsonRetorno ? 'Copiar json resposta' : ''}}"
                                                                validate-on="blur" readonly multiple maxlength="10000"
                                                                name="jsonRetorno" rows="10"
                                                                class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.logsPagamentoHistoricoObj.jsonRetorno ? (vm.jsonUtils.prettyPrint(vm.logsPagamentoHistoricoObj.jsonRetorno) || '') : '' }}</textarea>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-xs-12 col-md-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right;">
                                                        Json envio cancelamento:
                                                    </label>
                                                    <div class="input-group col-xs-12 col-md-9">
                                                        <a ng-click="vm.logsPagamentoHistoricoObj.jsonEnvioCancelamento ? vm.copiar(1) : null">
                                                            <textarea style="resize: none;" type="text"
                                                                ng-disabled="!vm.logsPagamentoHistoricoObj.jsonEnvioCancelamento"
                                                                tooltip-placement="left"
                                                                uib-tooltip="{{vm.logsPagamentoHistoricoObj.jsonEnvioCancelamento ? 'Copiar json envio' : ''}}"
                                                                validate-on="blur" readonly multiple maxlength="10000"
                                                                name="jsonEnvioCancelamento" rows="10"
                                                                class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.logsPagamentoHistoricoObj.jsonEnvioCancelamento ? (vm.jsonUtils.prettyPrint(vm.logsPagamentoHistoricoObj.jsonEnvioCancelamento) || '') : '' }}</textarea>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-12 col-md-6">
                                                <div class="form-group">
                                                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                                        style="text-align: right;">
                                                        Json resposta cancelamento:
                                                    </label>
                                                    <div class="input-group col-xs-12 col-md-9">
                                                        <a ng-click="vm.logsPagamentoHistoricoObj.jsonRetornoCancelamento ? vm.copiar(2) : null">
                                                            <textarea style="resize: none;" type="text"
                                                                ng-disabled="!vm.logsPagamentoHistoricoObj.jsonRetornoCancelamento"
                                                                tooltip-placement="left"
                                                                uib-tooltip="{{vm.logsPagamentoHistoricoObj.jsonRetornoCancelamento ? 'Copiar json resposta' : ''}}"
                                                                validate-on="blur" readonly multiple maxlength="10000"
                                                                name="jsonRetornoCancelamento" rows="10"
                                                                class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.logsPagamentoHistoricoObj.jsonRetornoCancelamento ? (vm.jsonUtils.prettyPrint(vm.logsPagamentoHistoricoObj.jsonRetornoCancelamento) || '') : '' }}</textarea>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <br>
                                        <hr-label dark="true" title="'Transações'"></hr-label>
                                        <br><br>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div ui-grid="vm.gridOptions"
                                                    ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;"
                                                    ui-grid-pinning ui-grid-save-state ui-grid-pagination
                                                    ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <hr />
                                <br />
                                <div class="row">
                                    <div class="form-group">
                                        <div class="col-md-12 col-lg-12 text-right">
                                            <button type="button" ng-click="vm.onClickVoltar(wizard)" class="btn btn-labeled btn-default">
                                                <span class="btn-label">
                                                    <i class="fa fa-arrow-circle-left"></i>
                                                </span>
                                                Voltar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>