(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('DetalhesTransacaoController', DetalhesTransacaoController);

    DetalhesTransacaoController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        '$stateParams',
        '$state'
    ];

    function DetalhesTransacaoController(
        BaseService, 
        $rootScope, 
        toastr, 
        $scope,
        $stateParams,
        $state) {
        
        var vm = this;
        vm.headerItems = [
            { name: 'Movimentações' }, 
            { name: 'Pagamentos Antecipados', state: 'pagamentos-antecipados.index' }, 
            { name: 'Detalhes do Pagamento', state: 'pagamentos-antecipados.detalhes-pagamento', params: { pagamentoId: $stateParams.pagamentoId, viagemId: $stateParams.viagemId } },
            { name: 'Detalhes da Transação' }
        ];
        
        vm.transacaoId = $stateParams.transacaoId;
        vm.pagamentoId = $stateParams.pagamentoId;
        vm.viagemId = $stateParams.viagemId;
        vm.transacao = {};
        vm.carregandoTransacao = true;

        // Funções
        vm.carregarTransacao = function() {
            vm.carregandoTransacao = true;
            
            BaseService.get('Viagem', 'ConsultarTransacaoPorId', { idTransacao: vm.transacaoId })
                .then(function(response) {
                    vm.carregandoTransacao = false;
                    
                    if (response && response.success) {
                        vm.transacao = response.data;
                        vm.formatarJsons();
                    } else {
                        toastr.error('Erro ao carregar dados da transação');
                        vm.voltarParaPagamento();
                    }
                })
                .catch(function(error) {
                    vm.carregandoTransacao = false;
                    toastr.error('Erro ao carregar dados da transação');
                    vm.voltarParaPagamento();
                });
        };

        vm.formatarJsons = function() {
            try {
                if (vm.transacao.jsonEnvio) {
                    vm.transacao.jsonEnvioFormatado = JSON.stringify(JSON.parse(vm.transacao.jsonEnvio), null, 2);
                }
            } catch (e) {
                vm.transacao.jsonEnvioFormatado = vm.transacao.jsonEnvio;
            }

            try {
                if (vm.transacao.jsonResposta) {
                    vm.transacao.jsonRespostaFormatado = JSON.stringify(JSON.parse(vm.transacao.jsonResposta), null, 2);
                }
            } catch (e) {
                vm.transacao.jsonRespostaFormatado = vm.transacao.jsonResposta;
            }
        };

        vm.voltarParaPagamento = function() {
            $state.go('pagamentos-antecipados.detalhes-pagamento', { 
                pagamentoId: vm.pagamentoId,
                viagemId: vm.viagemId 
            });
        };

        vm.voltarParaLista = function() {
            $state.go('pagamentos-antecipados.index');
        };

        vm.copiarJson = function(tipo) {
            var texto = '';
            if (tipo === 'envio') {
                texto = vm.transacao.jsonEnvioFormatado || vm.transacao.jsonEnvio || '';
            } else if (tipo === 'resposta') {
                texto = vm.transacao.jsonRespostaFormatado || vm.transacao.jsonResposta || '';
            }

            if (texto) {
                // Criar elemento temporário para copiar
                var tempElement = document.createElement('textarea');
                tempElement.value = texto;
                document.body.appendChild(tempElement);
                tempElement.select();
                document.execCommand('copy');
                document.body.removeChild(tempElement);
                
                toastr.success('JSON copiado para a área de transferência!');
            } else {
                toastr.warning('Não há conteúdo para copiar');
            }
        };

        // Inicialização
        vm.carregarTransacao();
    }
})();
