(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('DetalhesTransacaoController', DetalhesTransacaoController);

    DetalhesTransacaoController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        '$stateParams',
        '$state'
    ];

    function DetalhesTransacaoController(
        BaseService, 
        $rootScope, 
        toastr, 
        $scope,
        $stateParams,
        $state) {
        
        var vm = this;
        vm.headerItems = [
            { name: 'Movimentações' }, 
            { name: 'Pagamentos Antecipados', state: 'pagamentos-antecipados.index' }, 
            { name: 'Detalhes do Pagamento', state: 'pagamentos-antecipados.detalhes-pagamento', params: { pagamentoId: $stateParams.pagamentoId, viagemId: $stateParams.viagemId } },
            { name: 'Detalhes da Transação' }
        ];
        
        vm.transacaoId = $stateParams.transacaoId;
        vm.pagamentoId = $stateParams.pagamentoId;
        vm.viagemId = $stateParams.viagemId;
        vm.transacao = {};
        vm.carregandoTransacao = true;

        // Controles de visualização
        vm.modoVisualizacao = {
            envio: 'cards',    // 'cards' ou 'json'
            resposta: 'cards',  // 'cards' ou 'json'
            cancelamentoEnvio: 'cards',  // 'cards' ou 'json'
            cancelamentoResposta: 'cards'  // 'cards' ou 'json'
        };

        // Funções
        vm.carregarTransacao = function() {
            vm.carregandoTransacao = true;
            
            BaseService.get('Viagem', 'ConsultarTransacaoPorId', { idTransacao: vm.transacaoId })
                .then(function(response) {
                    vm.carregandoTransacao = false;
                    
                    if (response && response.success) {
                        vm.transacao = response.data;

                        // Mapear campos da API para o formato esperado pelo HTML
                        vm.transacao.jsonEnvio = vm.transacao.jsonEnvioDock;
                        vm.transacao.jsonResposta = vm.transacao.jsonRespostaDock;
                        vm.transacao.jsonEnvioCancelamento = vm.transacao.jsonEnvioDockCancelamento;
                        vm.transacao.jsonRespostaCancelamento = vm.transacao.jsonRespostaDockCancelamento;

                        // Mapear status para descrição
                        vm.transacao.statusDescricao = vm.getStatusDescricao(vm.transacao.status);

                        vm.formatarJsons();
                        vm.processarDadosParaCards();
                    } else {
                        toastr.error('Erro ao carregar dados da transação');
                        vm.voltarParaPagamento();
                    }
                })
                .catch(function(error) {
                    vm.carregandoTransacao = false;
                    toastr.error('Erro ao carregar dados da transação');
                    vm.voltarParaPagamento();
                });
        };

        vm.getStatusDescricao = function(status) {
            switch(status) {
                case 0: return 'Fechado';
                case 1: return 'Aberto';
                case 2: return 'Pendente';
                case 3: return 'Erro';
                case 4: return 'Cancelado';
                case 5: return 'Processando';
                case 6: return 'Não Executado';
                default: return 'Desconhecido';
            }
        };

        vm.alternarVisualizacao = function(tipo) {
            if (tipo === 'envio') {
                vm.modoVisualizacao.envio = vm.modoVisualizacao.envio === 'cards' ? 'json' : 'cards';
            } else if (tipo === 'resposta') {
                vm.modoVisualizacao.resposta = vm.modoVisualizacao.resposta === 'cards' ? 'json' : 'cards';
            } else if (tipo === 'cancelamento-envio') {
                vm.modoVisualizacao.cancelamentoEnvio = vm.modoVisualizacao.cancelamentoEnvio === 'cards' ? 'json' : 'cards';
            } else if (tipo === 'cancelamento-resposta') {
                vm.modoVisualizacao.cancelamentoResposta = vm.modoVisualizacao.cancelamentoResposta === 'cards' ? 'json' : 'cards';
            }
        };

        vm.processarDadosParaCards = function() {
            // Processar dados de envio
            try {
                if (vm.transacao.jsonEnvio) {
                    var dadosEnvio = JSON.parse(vm.transacao.jsonEnvio);
                    vm.dadosEnvioCards = {
                        contaOrigem: dadosEnvio.originalAccount,
                        contaDestino: dadosEnvio.destinationAccount,
                        valor: dadosEnvio.amount,
                        descricao: dadosEnvio.description
                    };

                    // Se description for um JSON, extrair campos
                    if (typeof dadosEnvio.description === 'string') {
                        try {
                            var descObj = JSON.parse(dadosEnvio.description);
                            vm.dadosEnvioCards.agencia = descObj.branchNumber;
                            vm.dadosEnvioCards.documento = descObj.nationalRegistration;
                            vm.dadosEnvioCards.referencia = descObj.description;
                            vm.dadosEnvioCards.protocolo = descObj.protocol;
                        } catch (e) {
                            vm.dadosEnvioCards.descricaoTexto = dadosEnvio.description;
                        }
                    }
                }
            } catch (e) {
                vm.dadosEnvioCards = {};
            }

            // Processar dados de resposta
            try {
                if (vm.transacao.jsonResposta) {
                    var dadosResposta = JSON.parse(vm.transacao.jsonResposta);
                    vm.dadosRespostaCards = {
                        codigoTransacao: dadosResposta.transactionCode,
                        contaOrigem: dadosResposta.originalAccount,
                        contaDestino: dadosResposta.destinationAccount,
                        valor: dadosResposta.amount,
                        dataTransacao: dadosResposta.transactionDate,
                        idAjuste: dadosResposta.idAdjustment,
                        idEmissor: dadosResposta.idIssuer,
                        idAjusteDestino: dadosResposta.idAdjustmentDestination
                    };
                }
            } catch (e) {
                vm.dadosRespostaCards = {};
            }

            // Processar dados de cancelamento - envio
            try {
                if (vm.transacao.jsonEnvioCancelamento) {
                    var dadosEnvioCancelamento = JSON.parse(vm.transacao.jsonEnvioCancelamento);
                    vm.dadosEnvioCancelamentoCards = {
                        contaOrigem: dadosEnvioCancelamento.originalAccount,
                        contaDestino: dadosEnvioCancelamento.destinationAccount,
                        valor: dadosEnvioCancelamento.amount,
                        descricao: dadosEnvioCancelamento.description
                    };

                    // Se description for um JSON, extrair campos
                    if (typeof dadosEnvioCancelamento.description === 'string') {
                        try {
                            var descObj = JSON.parse(dadosEnvioCancelamento.description);
                            vm.dadosEnvioCancelamentoCards.referencia = descObj.description;
                            vm.dadosEnvioCancelamentoCards.protocolo = descObj.protocol;
                        } catch (e) {
                            vm.dadosEnvioCancelamentoCards.descricaoTexto = dadosEnvioCancelamento.description;
                        }
                    }
                }
            } catch (e) {
                vm.dadosEnvioCancelamentoCards = {};
            }

            // Processar dados de cancelamento - resposta
            try {
                if (vm.transacao.jsonRespostaCancelamento) {
                    var dadosRespostaCancelamento = JSON.parse(vm.transacao.jsonRespostaCancelamento);
                    vm.dadosRespostaCancelamentoCards = {
                        codigoTransacao: dadosRespostaCancelamento.transactionCode,
                        contaOrigem: dadosRespostaCancelamento.originalAccount,
                        contaDestino: dadosRespostaCancelamento.destinationAccount,
                        valor: dadosRespostaCancelamento.amount,
                        dataTransacao: dadosRespostaCancelamento.transactionDate,
                        idAjuste: dadosRespostaCancelamento.idAdjustment,
                        idEmissor: dadosRespostaCancelamento.idIssuer,
                        idAjusteDestino: dadosRespostaCancelamento.idAdjustmentDestination
                    };
                }
            } catch (e) {
                vm.dadosRespostaCancelamentoCards = {};
            }
        };

        vm.formatarJsons = function() {
            // Formatar JSON de Envio
            try {
                if (vm.transacao.jsonEnvio) {
                    var jsonEnvio = JSON.parse(vm.transacao.jsonEnvio);

                    // Se o campo description for uma string JSON, fazer parse também
                    if (jsonEnvio.description && typeof jsonEnvio.description === 'string') {
                        try {
                            jsonEnvio.description = JSON.parse(jsonEnvio.description);
                        } catch (e) {
                            // Se não conseguir fazer parse, mantém como string
                        }
                    }

                    vm.transacao.jsonEnvioFormatado = JSON.stringify(jsonEnvio, null, 2);
                }
            } catch (e) {
                vm.transacao.jsonEnvioFormatado = vm.transacao.jsonEnvio;
            }

            // Formatar JSON de Resposta
            try {
                if (vm.transacao.jsonResposta) {
                    var jsonResposta = JSON.parse(vm.transacao.jsonResposta);

                    // Se o campo description for uma string JSON, fazer parse também
                    if (jsonResposta.description && typeof jsonResposta.description === 'string') {
                        try {
                            jsonResposta.description = JSON.parse(jsonResposta.description);
                        } catch (e) {
                            // Se não conseguir fazer parse, mantém como string
                        }
                    }

                    vm.transacao.jsonRespostaFormatado = JSON.stringify(jsonResposta, null, 2);
                }
            } catch (e) {
                vm.transacao.jsonRespostaFormatado = vm.transacao.jsonResposta;
            }

            // Formatar JSON de Envio Cancelamento
            try {
                if (vm.transacao.jsonEnvioCancelamento) {
                    var jsonEnvioCancelamento = JSON.parse(vm.transacao.jsonEnvioCancelamento);

                    // Se o campo description for uma string JSON, fazer parse também
                    if (jsonEnvioCancelamento.description && typeof jsonEnvioCancelamento.description === 'string') {
                        try {
                            jsonEnvioCancelamento.description = JSON.parse(jsonEnvioCancelamento.description);
                        } catch (e) {
                            // Se não conseguir fazer parse, mantém como string
                        }
                    }

                    vm.transacao.jsonEnvioCancelamentoFormatado = JSON.stringify(jsonEnvioCancelamento, null, 2);
                }
            } catch (e) {
                vm.transacao.jsonEnvioCancelamentoFormatado = vm.transacao.jsonEnvioCancelamento;
            }

            // Formatar JSON de Resposta Cancelamento
            try {
                if (vm.transacao.jsonRespostaCancelamento) {
                    var jsonRespostaCancelamento = JSON.parse(vm.transacao.jsonRespostaCancelamento);

                    // Se o campo description for uma string JSON, fazer parse também
                    if (jsonRespostaCancelamento.description && typeof jsonRespostaCancelamento.description === 'string') {
                        try {
                            jsonRespostaCancelamento.description = JSON.parse(jsonRespostaCancelamento.description);
                        } catch (e) {
                            // Se não conseguir fazer parse, mantém como string
                        }
                    }

                    vm.transacao.jsonRespostaCancelamentoFormatado = JSON.stringify(jsonRespostaCancelamento, null, 2);
                }
            } catch (e) {
                vm.transacao.jsonRespostaCancelamentoFormatado = vm.transacao.jsonRespostaCancelamento;
            }
        };

        vm.voltarParaPagamento = function() {
            $state.go('pagamentos-antecipados.detalhes-pagamento', { 
                pagamentoId: vm.pagamentoId,
                viagemId: vm.viagemId 
            });
        };

        vm.voltarParaLista = function() {
            $state.go('pagamentos-antecipados.index');
        };

        vm.copiarJson = function(tipo) {
            var texto = '';
            if (tipo === 'envio') {
                texto = vm.transacao.jsonEnvioFormatado || vm.transacao.jsonEnvio || '';
            } else if (tipo === 'resposta') {
                texto = vm.transacao.jsonRespostaFormatado || vm.transacao.jsonResposta || '';
            } else if (tipo === 'cancelamento-envio') {
                texto = vm.transacao.jsonEnvioCancelamentoFormatado || vm.transacao.jsonEnvioCancelamento || '';
            } else if (tipo === 'cancelamento-resposta') {
                texto = vm.transacao.jsonRespostaCancelamentoFormatado || vm.transacao.jsonRespostaCancelamento || '';
            }

            if (texto) {
                // Criar elemento temporário para copiar
                var tempElement = document.createElement('textarea');
                tempElement.value = texto;
                document.body.appendChild(tempElement);
                tempElement.select();
                document.execCommand('copy');
                document.body.removeChild(tempElement);

                toastr.success('JSON copiado para a área de transferência!');
            } else {
                toastr.warning('Não há conteúdo para copiar');
            }
        };

        // Inicialização
        vm.carregarTransacao();
    }
})();
