(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('CentralPendenciasDetalhesPagamentoController', CentralPendenciasDetalhesPagamentoController);

    CentralPendenciasDetalhesPagamentoController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        '$stateParams',
        '$state',
        'uiGridConstants'
    ];

    function CentralPendenciasDetalhesPagamentoController(
        BaseService,
        $rootScope,
        toastr,
        $scope,
        $stateParams,
        $state,
        uiGridConstants) {
        
        var vm = this;
        vm.headerItems = [
            { name: 'Movimentações' }, 
            { name: 'Central de Pendência Pagamentos Antecipados', state: 'central-pendencias-pagamentos-antecipados.index' }, 
            { name: 'Detalhes da Pendência' }
        ];
        
        vm.pagamentoId = $stateParams.pagamentoId;
        vm.viagemId = $stateParams.viagemId;
        vm.pagamento = {};
        vm.carregandoPagamento = true;

        // Controle de expansão dos blocos
        vm.blocosExpandidos = {
            informacoes: true,      // Informações principais sempre abertas
            proprietario: false,
            empresa: false,
            antecipacao: false,
            historico: false,
            transacoes: true        // Transações abertas por padrão se existirem
        };

        // Grid de Transações
        vm.gridTransacoes = {
            data: [],
            enableHorizontalScrollbar: 1,
            enableVerticalScrollbar: 1,
            enableSorting: true,
            enableFiltering: false,
            paginationPageSizes: [10, 25, 50],
            paginationPageSize: 10,
            columnDefs: [
                {
                    name: 'id',
                    displayName: 'ID',
                    width: 80,
                    type: 'number'
                },
                {
                    name: 'tipo',
                    displayName: 'Tipo',
                    width: 120
                },
                {
                    name: 'status',
                    displayName: 'Status',
                    width: 120,
                    cellTemplate: '<div class="ui-grid-cell-contents">' +
                        '<span class="label" ng-class="{' +
                        '\'label-success\': row.entity.status === \'Aprovado\',' +
                        '\'label-warning\': row.entity.status === \'Pendente\',' +
                        '\'label-danger\': row.entity.status === \'Cancelado\',' +
                        '\'label-info\': row.entity.status === \'Processando\'' +
                        '}">{{row.entity.status}}</span>' +
                        '</div>'
                },
                {
                    name: 'valor',
                    displayName: 'Valor',
                    width: 120,
                    cellFilter: 'currency:"R$ "',
                    type: 'number'
                },
                {
                    name: 'dataTransacao',
                    displayName: 'Data Transação',
                    width: 150
                },
                {
                    name: 'descricao',
                    displayName: 'Descrição',
                    width: 250
                }
            ]
        };

        // Funções
        vm.carregarPagamento = function() {
            vm.carregandoPagamento = true;
            
            BaseService.get('CentralPendencias', 'ConsultarPendenciaPagamentoPorId', {
                idCentralPendencias: vm.pagamentoId
            })
                .then(function(response) {
                    vm.carregandoPagamento = false;
                    
                    if (response && response.success) {
                        vm.pagamento = response.data;
                        console.log('Dados da pendência carregados:', vm.pagamento);

                        // Carregar transações se existirem
                        if (vm.pagamento.transacoes && vm.pagamento.transacoes.length > 0) {
                            vm.gridTransacoes.data = vm.pagamento.transacoes;
                        }
                    } else {
                        toastr.error('Erro ao carregar dados da pendência');
                        vm.voltar();
                    }
                })
                .catch(function(error) {
                    vm.carregandoPagamento = false;
                    console.error('Erro ao carregar pendência:', error);
                    toastr.error('Erro ao carregar dados da pendência');
                    vm.voltar();
                });
        };

        vm.formatarCpfCnpj = function(value) {
            if (!value) return '';
            var digits = value.replace(/\D/g, '');
            if (digits.length <= 11) {
                return digits.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
            } else {
                return digits.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
            }
        };

        vm.voltar = function() {
            $state.go('central-pendencias-pagamentos-antecipados.index');
        };

        // Inicialização
        vm.carregarPagamento();
    }
})();
