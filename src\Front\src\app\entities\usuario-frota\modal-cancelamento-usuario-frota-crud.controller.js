(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ModalCancelamentoUsuarioFrotaCrudController', ModalCancelamentoUsuarioFrotaCrudController);

        ModalCancelamentoUsuarioFrotaCrudController.$inject = ['$uibModalInstance', 'toastr', 'BaseService', 'IdUsuarioFrota'];

    function ModalCancelamentoUsuarioFrotaCrudController($uibModalInstance,toastr, BaseService, IdUsuarioFrota) {
        var vm = this;
        vm.saving = false;
        
        vm.cancelar = function() {
            var request = {
                Id: IdUsuarioFrota,
                Motivo: vm.motivoCancelamento
            };
            vm.saving = true;

            BaseService.post('UsuarioFrota', "Cancelar", request).then(function (response) {
                if (response.success) {
                    toastr.success('Usuário BBC Frota cancelado com sucesso!');
                    $uibModalInstance.close();
                } else {
                    toastr.error(response.message);
                    vm.saving = false;
                };
            });
        };
    } 
})();