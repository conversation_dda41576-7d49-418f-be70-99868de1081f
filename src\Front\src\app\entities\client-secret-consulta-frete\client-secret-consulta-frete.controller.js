(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ClientSecretConsultaFreteController', ClientSecretConsultaFreteController);

    ClientSecretConsultaFreteController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function ClientSecretConsultaFreteController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Client Secret Consulta Frete'
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };
        
        vm.gridOptions = {
            data: [],  
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
               url: "ClientSecretAdm/ConsultarGridClientSecretAdm"
            },
            columnDefs: [
                {
                    name: '<PERSON><PERSON><PERSON><PERSON>',
                    field: 'acoes',
                    width: 120,
                    enableSorting: false,
                    enableFiltering: false,
                    cellTemplate: '<div class="ui-grid-cell-contents text-center">' +
                        '<button type="button" class="btn btn-xs btn-info" ng-click="grid.appScope.vm.editar(row.entity.id)" tooltip-placement="right" uib-tooltip="Editar" ng-disabled="row.entity.ativo == 0">' +
                        '<i class="fa fa-edit"></i>' +
                        '</button> ' +
                        '<button type="button" class="btn btn-xs" ng-class="row.entity.ativo == 0 ? \'btn-danger\' : \'btn-success\'" ng-click="grid.appScope.vm.alterarStatus(row.entity)" tooltip-placement="right" uib-tooltip="{{row.entity.ativo == 1 ? \'Desativar\' : \'Ativar\'}}" ng-disabled="row.entity.ativo == 0">' +
                        '<i class="fa" ng-class="row.entity.ativo == 0 ? \'fa-times\' : \'fa-check\'"></i>' +
                        '</button>' +
                        '</div>'
                },
                {
                    name: 'Código',
                    width: 80,
                    type : 'number',
                    primaryKey: true,
                    field: 'id',
                    serverField: 'id'
                },
                {
                    name: 'Login',
                    field: 'login',
                    width: 150,
                    serverField: 'login'
                },
                {
                    name: 'Status',
                    field: 'ativo',
                    width: 80,
                    serverField: 'ativo',
                    cellTemplate: '<div class="ui-grid-cell-contents text-center">' +
                        '<span class="label" ng-class="row.entity.ativo == 1 ? \'label-success\' : \'label-danger\'">' +
                        '{{row.entity.ativo == 1 ? " Ativo " : "Inativo"}}' +
                        '</span>' +
                        '</div>'
                },
                {
                    name: 'Client secret',
                    width: '*',
                    minWidth: 180,
                    field: 'clientSecret',
                    serverField: 'secretKeySearch',
                    enableFiltering: false
                },
                {
                    name: 'Descrição',
                    field: 'descricao',
                    width: 200,
                    serverField: 'descricao'
                },
                {
                    name: 'Data Cadastro',
                    field: 'dataCadastro',
                    width: 130,
                    enableFiltering: false
                },
                {
                    name: 'Data Alteração',
                    field: 'dataAlteracao',
                    width: 130,
                    enableFiltering: false
                },
                {
                    name: 'Usuário Cadastro',
                    field: 'usuarioCadastro',
                    width: 150,
                    enableFiltering: false
                },
                {
                    name: 'Usuário Alteração',
                    field: 'usuarioAlteracao',
                    width: 150,
                    enableFiltering: false
                }
            ]
        };

        vm.editar = function (id) {
            $state.go('client-secret-consulta-frete.client-secret-consulta-frete-crud', { link: id });
        };

        vm.alterarStatus = function (entity) {
            var novoStatus = entity.ativo == 1 ? 0 : 1;
            var mensagem = novoStatus == 1 ? 'ativar' : 'desativar';
            
            BaseService.post('ClientSecretAdm', 'AlterarStatus', {
                id: entity.id,
                ativo: novoStatus
            }).then(function (response) {
                if (response.success) {
                    toastr.success(response.message);
                    vm.gridOptions.dataSource.refresh();
                } else {
                    toastr.error(response.message);
                }
            });

         
        };

        vm.visualizarClientSecret = function (clientSecret) {
            // Implementar modal para visualizar client secret
            alert('Client Secret: ' + clientSecret);
        };
    }
})();
