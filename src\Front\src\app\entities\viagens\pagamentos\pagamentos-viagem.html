<div id="PagamentosViagemController" ng-controller="PagamentosViagemController as vm">
    <form-header items="vm.headerItems" head="'Pagamentos'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Painel de pagamentos Viagem</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <div form-wizard steps="2" class="row">
                            <div class="form-wizard">
                                <ol class="row">
                                    <li ng-class="{'active':wizard.active(1)}"
                                        ng-click="wizard.go(1); vm.gridOptions.dataSource.refresh();"
                                        class="control-label col-xs-12 col-md-6" style="text-align: center;">
                                        <h4>Detalhes da viagem</h4>
                                    </li>
                                    <li ng-class="{'active':wizard.active(2)}"
                                        ng-click="wizard.go(2); vm.gridPagamentoHistoricoOptions.dataSource.refresh()"
                                        class="control-label col-xs-12 col-md-6" style="text-align: center;">
                                        <h4>Histórico Pagamentos</h4>
                                    </li>
                                </ol>
                                <div id="activateTab1" ng-show="wizard.active(1)">
                                    <div ng-include="'app/entities/viagens/pagamentos/abas/aba-detalhes-viagem.html'"
                                        class="form-horizontal"> </div>
                                </div>
                                <div id="activateTab2" ng-show="wizard.active(2)">
                                    <div ng-include="'app/entities/viagens/pagamentos/abas/aba-historico-pagamentos.html'"
                                        class="form-horizontal"> </div>
                                </div>
                            </div>
                            <hr />
                            <br />
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-md-12 col-lg-12 text-right">
                                        <button type="button" ng-click="vm.onClickVoltar(wizard)"
                                            class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-left"></i>
                                            </span>
                                            Voltar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>