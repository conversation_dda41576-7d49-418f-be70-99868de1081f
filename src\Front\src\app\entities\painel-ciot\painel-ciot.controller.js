(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelCiotController', PainelCiotController);

    PainelCiotController.inject = [
        'URL_SERVER_DEV', 
        'BaseService', 
        '$window',
        '$rootScope', 
        'toastr', 
        '$scope', 
        'PersistentDataService', 
        '$timeout', 
        '$state', 
        'PERFIL_ADMINISTRADOR', 
        'oitozero.ngSweetAlert', 
        '$uibModal'];

    function PainelCiotController(
        URL_SERVER_DEV, 
        BaseService, 
        $window,
        $rootScope, 
        toastr, 
        $scope, 
        PersistentDataService, 
        $timeout, 
        $state, 
        PERFIL_ADMINISTRADOR, 
        SweetAlert, 
        $uibModal) {
        var vm = this;
        var msg = "Deseja encerrar o CIOT?";
        vm.CiotId = 0;
        vm.Ciot = "";
        vm.DtEmissao = "";
        vm.DtFim = "";
        vm.CpfCnpjCliente = "";
        vm.permiteEncerramentoCiot;

        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Painel de CIOT'
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == PERFIL_ADMINISTRADOR;
        };

        vm.alterarStatusCancelado = function (ciotId, ciot, dtEmissao, dtFim, cpfCnpjCliente) {
            vm.CiotId = ciotId;
            vm.Ciot = ciot; 
            vm.DtEmissao = dtEmissao; 
            vm.DtFim = dtFim;
            vm.CpfCnpjCliente = cpfCnpjCliente;             
            vm.insereMotivo();
        };
       
        vm.date = {
            startDate: moment().add(-30, 'days'),
            endDate: moment()
        };

        vm.dateOptions = {
            timePicker: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'
                ],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        vm.codViagemExterno = null;

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
        }

        vm.alterarStatusEncerrado = function (id) {
            Sistema.Msg.confirm(msg, function () {
                BaseService.post('PainelCiot', 'AlterarStatusEncerrado', {
                    id: id
                }).then(function (response) {
                    response.success ? toastr.success('CIOT encerrado com sucesso!') : toastr.error(response.message);
                    vm.gridOptions.dataSource.refresh();
                });
            }, function () {
                    
            });
        };

        vm.insereMotivo = function() {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-ciot/modal/modal-cancelamento.html',
                controller: 'PainelCiotModalCancelCrudController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'md',
                resolve: {
                    CiotId: function() {
                        return vm.CiotId;
                    },
                    Ciot: function() {
                        return vm.Ciot;
                    },
                    DtEmissao: function() {
                        return vm.DtEmissao;
                    },
                    DtFim: function() {
                        return vm.DtFim;
                    },
                    CpfCnpjCliente: function() {
                        return vm.CpfCnpjCliente;
                    }
                }
            }).result.then(function() {
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.retificarCiot = function(ciotId, ciot, cpfCnpjProprietario, valorTarifas, quantidadeTarifas, rntrc, cpfCnpjCliente) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-ciot/modal/modal-retificar.html',
                controller: 'PainelCiotModalRetificaCrudController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                windowClass: 'custom-modal-width',
                resolve: {
                    CiotId: function() {
                        return ciotId;
                    },
                    Ciot: function() {
                        return ciot;
                    },
                    CpfCnpjProprietario: function() {
                        return cpfCnpjProprietario;
                    },
                    QuantidadeTarifas: function() {
                        return quantidadeTarifas;
                    },
                    ValorTarifas: function() {
                        return valorTarifas;
                    },
                    Rntrc: function() {
                        return rntrc;
                    },
                    CpfCnpjCliente: function() {
                        return cpfCnpjCliente;
                    }
                }
            }).result.then(function() {
                vm.gridOptions.dataSource.refresh();
            });
        };


        vm.encerrarCiot = function(ciotId, verificadorCiot,ciot, cpfCnpjCliente) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-ciot/modal/modal-encerramento.html',
                controller: 'PainelCiotModalEncerraCrudController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                windowClass: 'custom-modal-width',
                resolve: {
                    CiotId: function() {
                        return ciotId;
                    },
                    Ciot: function() {
                        return ciot;
                    },
                    VerificadorCiot: function() {
                        return verificadorCiot;
                    },
                    CpfCnpjCliente: function() {
                        return cpfCnpjCliente;
                    }
                }
            }).result.then(function() {
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.impressao = function (id) {
            var msg = 'Deseja imprimir o CIOT?';

            Sistema.Msg.confirm(msg, function () {
                var IdCiot = { id: id };
                var url = URL_SERVER_DEV + "PainelCiot" + "/ImprimirCiot";
                var data = JSON.stringify(IdCiot);
                var args = { json: data };
                BaseService.openWindowWithPost(url, args);
            }, function () {      
            });
        };

        function currentDateExportar() {
            return new Date().toLocaleString().replace(/[\/\-:,]/g, "_").replace(/\s/g, "");
         }

        function exportarEmPdf(gridName) {
            vm.desabilitarBtnRelatorio = true;
            vm[gridName].dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmPdfNovo(
                        "#exportable",
                        "Relatório de CIOT",
                        "BBC_Relatorio_CIOT_" + currentDateExportar()
                    )
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            }, gridName);
            vm.desabilitarBtnRelatorio = false;
        }

        function exportarEmExcel(gridName, formatoXls) {
            vm.desabilitarBtnRelatorio = true;
            vm[gridName].dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmExcel2("exportable-xls", "BBC_Relatorio_CIOT_" +  currentDateExportar())
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            }, gridName);
            vm.desabilitarBtnRelatorio = false;
        }

        vm.exportarRelatorio = function (gridName, extensao) {
            switch (extensao){
                case 1: {
                    exportarEmExcel(gridName, true)
                    break;
                }
                case 2: {
                    exportarEmPdf(gridName)
                    break;
                }
                default: exportarEmPdf(gridName)
                    break;
            }
        };

        vm.abrirModalRelatorio = function (gridName, controllerPai) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-ciot/modal/modal-relatorios-operacao-transporte.html',
                controller: function ($uibModalInstance, $uibModalStack) {
                    var vm = this;

                    vm.modalRelatorioOptions = [{}];

                    vm.headerItems = [{name: 'CIOT'}];

                    for (var x in controllerPai[gridName].columnDefs) {
                        vm.modalRelatorioOptions[x] = {
                            name: controllerPai[gridName].columnDefs[x].displayName,
                            field: controllerPai[gridName].columnDefs[x].field,
                            pipe: controllerPai[gridName].columnDefs[x].pipe,
                            visible: controllerPai[gridName].columnDefs[x].visible,
                            enabled: true
                        }
                    }

                    vm.fechar = function () {
                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        $uibModalStack.dismissAll();
                    }

                    vm.exportarRelatorio = function (extensao) {

                        if (vm.modalRelatorioOptions.filter(function(x){return x.enabled}).length < 2) {
                            toastr.error("Escolha alguma coluna para gerar o relatório.");
                            return;
                        }

                        if (controllerPai.date.endDate.diff(controllerPai.date.startDate, 'days') > 30) {
                            toastr.error("Escolha um período de no máximo 30 dias para gerar o relatório.")
                            return;
                        }

                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        controllerPai.exportarRelatorio(gridName, extensao);

                        $uibModalStack.dismissAll();
                    }

                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg'
            }).result.then(function () {
            });
        };

        vm.consultaParametroEncerramentoEmpresa = function (ciot) {
            BaseService.get('Empresa', 'ConsultaParametroEmpresaPermiteEncerramentoCiot').then(function (response) {
                    if (response.success) {
                        vm.permiteEncerramentoCiot = response.data;
                    } else {
                        return toastr.error(response.message)
                    }
                })
        }

        function init() {
            vm.consultaParametroEncerramentoEmpresa();
        }

        init();


        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "PainelCiot/ConsultarGridOperacaoTransporte",
            dataSource: {
                url: "PainelCiot/ConsultarGridOperacaoTransporte",
                params: function () {
                    return {
                        DtInicial: vm.date.startDate.format('DD/MM/YYYY').toString(),
                        DtFinal: vm.date.endDate.format('DD/MM/YYYY').toString(),
                        CnpjEmpresa: vm.consultaEmpresa.selectedValue ? [vm.consultaEmpresa.selectedValue.replace(/[^\w]/g, '')] : []
                    }
                },
            },  
            columnDefs: [{
                name: 'Ações',
                width: 120,
                cellTemplate: '<div ng-if="!row.groupHeader"  class="ui-grid-cell-contents" title="TOOLTIP" style="text-align: left">\
                    <button tooltip-placement="right" uib-tooltip="Visualizar" type="button" ui-sref="painel-ciot.painel-ciot-crud({link: row.entity.id, status: row.entity.status, visualizar: true, ciot: row.entity.ciot})"\
                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                        <i class="fa fa-eye"></i>\
                    </button>\
                    <button tooltip-placement="right" uib-tooltip="Impressão" type="button" ng-click="grid.appScope.vm.impressao(row.entity.id)")"\
                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                        <i class="fa fa-print"></i>\
                    </button>\
                    <div class="container-btn-action" ng-show="row.entity.status=== 1 && row.entity.encerrado=== \'N\'">\
                        <button tooltip-placement="right" uib-tooltip="Retificar" type="button" ng-click="grid.appScope.vm.retificarCiot(row.entity.id, row.entity.ciot, row.entity.proprietario, row.entity.valorTarifas, row.entity.quantidadeTarifas, row.entity.rntrc, row.entity.contratante)"\
                            ng-class="{ \'btn btn-xs btn-warning\': true }">\
                            <i class="fa fa-edit"></i>\
                        </button>\
                    </div>\
                    <div class="container-btn-action" ng-show="row.entity.status=== 1 && row.entity.encerrado=== \'N\'">\
                        <button tooltip-placement="right" uib-tooltip="Cancelar" type="button" ng-click="grid.appScope.vm.alterarStatusCancelado(row.entity.id, row.entity.ciot, row.entity.dtEmissao, row.entity.dtTerminoFrete, row.entity.contratante)"\
                            ng-class="{ \'btn btn-xs btn-danger\': true }">\
                            <i class="fa fa-times"></i>\
                        </button>\
                    </div>\
                    <div class="container-btn-action" ng-show="row.entity.status=== 1 && row.entity.tipoCarga=== 3 && row.entity.encerrado=== \'N\'">\
                        <button tooltip-placement="right" uib-tooltip="Encerrar" ng-disabled="!grid.appScope.vm.permiteEncerramentoCiot" type="button" ng-click="grid.appScope.vm.encerrarCiot(row.entity.id, row.entity.verificadorCiot, row.entity.ciot, row.entity.contratante)"\
                            ng-class="{ \'btn btn-xs btn-success\': true }">\
                            <i class="fa fa-flag-checkered"></i>\
                        </button>\
                    </div>\
                </div>'
            }, {
                displayName: 'Código',
                name: 'Código',
                serverField: 'ID_OPTRANSPORTE',
                field: 'id',
                minWidth: 80,
                width: 80,
                type: 'number',
                enableGrouping: false,
            }, {
                displayName: 'Protocolo Ciot',
                name: 'Protocolo Ciot',
                serverField: 'CIOT',
                field: 'protocolo',
                width: 150,
                minWidth: 150,
                enableGrouping: false
            }, {
                displayName: 'Ciot',
                name: 'Ciot',
                serverField: 'CIOT',
                field: 'ciot',
                width: 150,
                minWidth: 150,
                enableGrouping: false,
                visible: false
            }, {
                displayName: 'Dígito verificador Ciot',
                name: 'Digito verificador Ciot',
                serverField: 'VERIFICADOR_CIOT',
                field: 'verificadorCiot',
                width: 150,
                minWidth: 150,
                enableGrouping: false,
                visible: false
            }, {
                displayName: 'Cpf cnpj Cliente',
                name: 'Cliente',
                serverField: 'CLIENTE.CPFCNPJ',
                field: 'cpfCnpjCliente',
                width: 150,
                minWidth: 150,
                enableGrouping: false,
                visible: false
            }, {
                displayName: 'Tipo Ciot',
                name: 'Tipo Carga',
                serverField: 'TIPO_VIAGEM',
                field: 'tipoCarga',
                minWidth: 120,
                width: 120,
                enableGrouping: false,
                enableFiltering: true,
                enum: true,
                pipe: function (input) {
                    return filterTipoCiot(input)
                },
                enumTipo: 'ETipoCIOT',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.tipoCarga === 1"> Padrão </p>\
                                        <p ng-show="row.entity.tipoCarga === 3"> Tac Agregado </p>\
                                   </div>'
            }, {
                displayName: 'Status',
                name: 'Status',
                serverField: 'Status',
                field: 'status',
                minWidth: 130,
                width: 130,
                enableGrouping: false,
                enum: true,
                pipe: function (input) {
                    return filterStatus(input)
                },
                enumTipo: 'EStatusCIOT',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.status === 1"> Ciot Gerado </p>\
                                        <p ng-show="row.entity.status === 2"> Contingência </p>\
                                        <p ng-show="row.entity.status === 3"> Cancelado </p>\
                                   </div>'
            }, {
                displayName: 'Encerrado',
                name: 'Encerrado',
                serverField: 'ENCERRADO',
                field: 'encerrado',
                minWidth: 120,
                width: 120,
                type: 'string',
                enableGrouping: false,
                enum: true,
                pipe: function (input) {
                    return filterSimNao(input)
                },
                enumTipo: 'ECiotEncerrado',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.encerrado === \'S\'"> Sim </p>\
                                        <p ng-show="row.entity.encerrado === \'N\'"> Não </p>\
                                   </div>'
            }, {
                displayName: 'Data Encerramento',
                name: 'DataEncerramento',
                serverField: 'OPERACAO_ENCERRAMENTO.DT_ENCERRAMENTO',
                field: 'dtEncerramento',
                minWidth: 140,
                width: '*',
                type: 'date' ,
                enableGrouping: false,                
                enableFiltering: true
            },{
                displayName: 'Contingência',
                name: 'Contingência',
                serverField: 'CONTINGENCIA',
                field: 'contingencia',
                minWidth: 120,
                width: 120,
                enableGrouping: false,
                enum: true,
                pipe: function (input) {
                    return filterSimNao(input)
                },
                enumTipo: 'EContingenciaCiot',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.contingencia === \'S\'"> Sim </p>\
                                        <p ng-show="row.entity.contingencia === \'N\'"> Não </p>\
                                   </div>',
                visible: false
            }, {
                displayName: 'Razão Social',
                name: 'RazaoSocial',
                serverField: 'CONTRATANTE.PESSOA.NOME_RZSOCIAL',
                field: 'razaoSocial',
                minWidth: 150,
                width: 150,
                enableGrouping: false,
            },{
                displayName: 'Contratante',
                name: 'Contratante',
                serverField: 'CONTRATANTE.CPFCNPJ',
                field: 'contratante',
                minWidth: 150,
                width: 150,
                enableGrouping: false,
            },{
                displayName: 'Proprietário',
                name: 'Proprietário',
                serverField: 'PROPRIETARIO.CPFCNPJ',
                field: 'proprietario',
                minWidth: 150,
                width: 150,
                enableGrouping: false,
            },{
                displayName: 'Motorista',
                name: 'Motorista',
                serverField: 'MOTORISTA.CPF_CNPJ',
                field: 'motorista',
                minWidth: 150,
                width: 150,
                enableGrouping: false,
            }, {
                displayName: 'Emissão',
                name: 'Emissão',
                serverField: 'DT_CADASTRO',
                field: 'dtEmissao',
                minWidth: 140,
                type: 'date' ,
                width: '*',
                enableGrouping: false,                
                enableFiltering: true
            }, {
                displayName: 'Início',
                name: 'Início',
                serverField: 'DT_INICIO_FRETE',
                field: 'dtInicioFrete',
                minWidth: 140,
                width: '*',
                type: 'date' ,
                enableGrouping: false,                
                enableFiltering: true
            }, {
                displayName: 'Término',
                name: 'Término',
                serverField: 'DT_TERMINO_FRETE',
                field: 'dtTerminoFrete',
                minWidth: 140,
                width: '*',
                type: 'date' ,
                enableGrouping: false,                
                enableFiltering: true
            }, {
                displayName: 'RNTRC',
                name: 'RNTRC',
                serverField: 'PROPRIETARIO.RNTRC',
                field: 'rntrc',
                minWidth: 120,
                width: 120,
                enableGrouping: false
            }, {
                displayName: 'Valor Frete',
                name: 'Valor Frete',
                serverField: 'VL_FRETE',
                field: 'valorFrete',
                minWidth: 100,
                width: 100,
                enableGrouping: false,
                type: 'string',
                enableFiltering: false
            }, 
            {
                displayName: 'Valor Tarifas',
                name: 'Valor Tarifas',
                serverField: 'VL_TARIFAS',
                field: 'valorTarifas',
                minWidth: 100,
                width: 100,
                type: 'string',
                enableGrouping: false,
                visible: false
            },
            {
                displayName: 'Quantidade Tarifas',
                name: 'Quantidade Tarifas',
                serverField: 'QT_TARIFAS',
                field: 'quantidadeTarifas',
                minWidth: 100,
                width: 100,
                enableGrouping: false,
                visible: false
            },{
                displayName: 'Servidor',
                name: 'Servidor',
                serverField: 'SERVIDOR',
                field: 'servidor',
                minWidth: 130,
                width: '*',
                enableGrouping: false,
            }]
        };

        function filterStatus(input) {
            var correcao;
            switch (input) {
                case 1:
                    correcao = "Ciot Gerado"
                    break;
                case 2:
                    correcao = "Contingência"
                    break;
                case 3:
                    correcao = "Cancelado"
                    break;
                default:
                    correcao = input;
            }
            return correcao
        }

        function filterSimNao(input) {
            var correcao;
            switch (input) {
                case "S":
                    correcao = "Sim"
                    break;
                case "N":
                    correcao = "Não"
                    break;
                default:
                    correcao = input;
            }
            return correcao
        }

        function filterTipoCiot(input) {
            var correcao;
            switch (input) {
                case 1:
                    correcao = "Padrão"
                    break;
                case 3:
                    correcao = "Tac agregado"
                    break;
                default:
                    correcao = input;
            }
            return correcao
        }

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }, {
                name: 'CNPJ',
                field: 'cnpj',
                width: 120
            }],
            desiredValue: 'cnpj',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        }

        vm.isEmpresa = function () {
            return $rootScope.usuarioLogado.empresaId > 0;
        };

        vm.getNomeEmpresaUsuarioLogado = function () {
            return $window.localStorage.getItem("empresaNome");
        }


        // Controle de aba!!
        $scope.$on('$stateChangeStart', function (event, toState) {
            // Se estiver navegando para uma tela diferente (não para o detalhe do CIOT)
            if (toState.name !== 'painel-ciot.painel-ciot-crud') {
                // Remove o cache ao invés de armazená-lo
                PersistentDataService.remove('PainelCiotController');
            } else {
                // Mantém o comportamento atual apenas quando navega para o detalhe
                PersistentDataService.store('PainelCiotController', vm, "Painel CIOT", "PainelCiotCrudController", "painel-ciot.index");
            }
        });

        var selfScope = PersistentDataService.get('PainelCiotController');
        var filho = PersistentDataService.get('PainelCiotCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('painel-ciot.painel-ciot-crud', {
                    link: filho.data.painelCiot.id > 0 ? filho.data.painelCiot.id : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();
