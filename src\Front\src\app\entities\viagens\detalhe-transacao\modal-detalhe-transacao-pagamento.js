(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ModalDetalhesTransacaoPagamentoController', ModalDetalhesTransacaoPagamentoController);

    ModalDetalhesTransacaoPagamentoController.$inject = ['$uibModalInstance', 'toastr', 'BaseService', '$timeout', 'idTransacao'];

    function ModalDetalhesTransacaoPagamentoController($uibModalInstance, toastr, BaseService, $timeout, idTransacao) {
        var vm = this;
        vm.modal = {};

        init();

        function init() {
            BaseService.get('Viagem', 'ConsultarTransacaoPorId', {
                idTransacao: idTransacao
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    $uibModalInstance.close();
                    return;
                } else {
                    vm.modal = response.data;
                }
            });
        }

        vm.copiar = function (campo) {
            switch (campo) {
                case 0:
                    unsecuredCopyToClipboard(vm.modal.jsonEnvioDock);
                    break;
                case 1:
                    unsecuredCopyToClipboard(vm.modal.jsonRetornoDock);
                    break;
                case 2:
                    unsecuredCopyToClipboard(vm.modal.jsonEnvioDockCancelamento);
                    break;
                case 3:
                    unsecuredCopyToClipboard(vm.modal.jsonRetornoDockCancelamento);
                    break;
                default:
                    unsecuredCopyToClipboard(vm.modal.jsonEnvioDock)
                    break;   
            }
            toastr.success("Json copiado para área de transferência");
        }

        function unsecuredCopyToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
            } catch (err) {
                console.error('Unable to copy to clipboard', err);
            }
            document.body.removeChild(textArea);
        }

        vm.prettyPrint = function (objJson) {
            if (!objJson) {
                return ''; // Retorna uma string vazia ou mensagem alternativa
            }
            try {
                var obj = JSON.parse(objJson);
                var pretty = JSON.stringify(obj, undefined, 4);
                return pretty;
            } catch (error) {
                console.error('Erro ao fazer o parsing do JSON:', error);
                return ''; // Em caso de erro, retorna uma string vazia ou mensagem alternativa
            }
        }

    }
})();