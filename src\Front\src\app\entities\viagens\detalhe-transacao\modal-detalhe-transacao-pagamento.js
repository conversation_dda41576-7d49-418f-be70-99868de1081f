(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ModalDetalhesTransacaoPagamentoController', ModalDetalhesTransacaoPagamentoController);

    ModalDetalhesTransacaoPagamentoController.$inject = ['$uibModalInstance', 'toastr', 'BaseService', '$timeout', 'idTransacao', 'JsonUtilsService'];

    function ModalDetalhesTransacaoPagamentoController($uibModalInstance, toastr, BaseService, $timeout, idTransacao, JsonUtilsService) {
        var vm = this;
        vm.modal = {};

        // Usar o serviço compartilhado para funções JSON
        vm.jsonUtils = JsonUtilsService;

        init();

        function init() {
            BaseService.get('Viagem', 'ConsultarTransacaoPorId', {
                idTransacao: idTransacao
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    $uibModalInstance.close();
                    return;
                } else {
                    vm.modal = response.data;
                }
            });
        }

        vm.copiar = function (campo) {
            var jsonToCopy;
            var label;

            switch (campo) {
                case 1:
                    jsonToCopy = vm.modal.jsonEnvioDock;
                    label = 'JSON de envio';
                    break;
                case 2:
                    jsonToCopy = vm.modal.jsonRespostaDock;
                    label = 'JSON de resposta';
                    break;
                case 3:
                    jsonToCopy = vm.modal.jsonEnvioDockCancelamento;
                    label = 'JSON de envio do cancelamento';
                    break;
                case 4:
                    jsonToCopy = vm.modal.jsonRespostaDockCancelamento;
                    label = 'JSON de resposta do cancelamento';
                    break;
                default:
                    jsonToCopy = vm.modal.jsonEnvioDock;
                    label = 'JSON de envio';
                    break;
            }

            JsonUtilsService.copyToClipboard(jsonToCopy, label + ' copiado para área de transferência');
        }

    }
})();