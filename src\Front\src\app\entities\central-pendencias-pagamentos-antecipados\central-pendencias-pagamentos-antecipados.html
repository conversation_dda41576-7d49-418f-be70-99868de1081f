<div ng-controller="CentralPendenciasPagamentosAntecipadosController as vm">
    <form-header items="vm.headerItems" head="'Central de Pendências - Pagamentos Antecipados'"
        state="central-pendencias-pagamentos-antecipados">
    </form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins" style="border-radius: 8px;">
                    <div class="ibox-title"
                        style="display: flex; align-items: center; justify-content: space-between; min-height: 50px;">
                        <div style="display: flex; align-items: center;">
                            <h5 style="margin: 0; display: flex; align-items: center;">
                                <span class="hidden-xs">Central de Pendências - Pagamentos Antecipados</span>
                                <span class="visible-xs-inline">Central Pendências</span>
                            </h5>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary btn-sm" ng-click="vm.consultar()"
                                    uib-tooltip="Consultar dados"
                                    style="border-top-left-radius: 6px; border-bottom-left-radius: 6px;">
                                    <i class="fa fa-search"></i>
                                    <span class="hidden-xs"> Consultar</span>
                                </button>
                                <button type="button" class="btn btn-default btn-sm" ng-click="vm.limparFiltros()"
                                    uib-tooltip="Limpar filtros">
                                    <i class="fa fa-eraser"></i>
                                    <span class="hidden-xs"> Limpar</span>
                                </button>
                                <button type="button" class="btn btn-info btn-sm"
                                    ng-click="vm.gridOptions.dataSource.refresh()" uib-tooltip="Atualizar grid"
                                    style="border-top-right-radius: 6px; border-bottom-right-radius: 6px;">
                                    <i class="fa fa-refresh"></i>
                                    <span class="hidden-xs"> Atualizar</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="ibox-content">
                        <div class="alert alert-warning alert-dismissible" ng-show="!vm.alertaFechado"
                            style="border-radius: 6px;">
                            <button type="button" class="close" ng-click="vm.fecharAlerta()" aria-label="Fechar">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <div style="display: flex; align-items: flex-start;">
                                <i class="fa fa-exclamation-triangle"
                                    style="font-size: 20px; margin-right: 10px; margin-top: 2px; color: #f0ad4e;"></i>
                                <div>
                                    <strong>Atenção!</strong> Esta tela apresenta pagamentos com pendências que requerem
                                    atenção:
                                    <ul style="margin: 8px 0 0 0; padding-left: 20px;">
                                        <li><strong>Status Aberto</strong> com Data Previsão Pagamento vencida (menor
                                            que hoje)</li>
                                        <li><strong>Status Cancelado</strong> com Status Antecipação = Aprovado</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="panel panel-default" style="border-radius: 6px; overflow: hidden;">
                            <div class="panel-heading" style="cursor: pointer;" ng-click="vm.toggleFiltros()">
                                <h6 class="panel-title"
                                    style="display: flex; align-items: center; justify-content: space-between; margin: 0;">
                                    <span>
                                        <i class="fa fa-filter"></i> Filtros de Pesquisa
                                    </span>
                                    <i class="fa fa-chevron-down"
                                        ng-style="{'transform': vm.filtrosExpandidos ? 'rotate(180deg)' : 'rotate(0deg)'}"
                                        style="transition: transform 0.3s ease;"></i>
                                </h6>
                            </div>
                            <div class="panel-body" ng-show="vm.filtrosExpandidos" style="transition: all 0.3s ease;">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="text-left">Período</label>
                                            <input type="text" class="form-control" ng-model="vm.filtros.periodo"
                                                date-range-picker options="vm.dateOptions"
                                                placeholder="Selecione o período">
                                        </div>
                                    </div>

                                    <div class="col-md-4" ng-show="vm.usuAdm">
                                        <div class="row">
                                            <label class="text-left">Empresa</label>
                                            <div class="form-group" ng-show="vm.usuAdm">
                                                
                                                <consulta-padrao-modal tabledefinition="vm.consultaEmpresa"
                                                    idname="consultaEmpresa" placeholder="'Selecione uma Empresa'"
                                                    required-message="'Empresa é obrigatória'"
                                                    ng-required="vm.isAdmin()" directivesizes="'col-xs-12'"
                                                    labelsize="'sr-only'">
                                                </consulta-padrao-modal>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="text-left">CPF/CNPJ Proprietário</label>
                                            <input type="text" class="form-control"
                                                ng-model="vm.filtros.cpfCnpjProprietario"
                                                placeholder="Digite o CPF/CNPJ"
                                                ui-br-cpfcnpj-mask
                                                maxlength="18">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="margin-top: 15px;">
                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="alert alert-info" style="margin-bottom: 10px; padding: 10px;">
                                    <div style="text-align: center;">
                                        <i class="fa fa-calendar-o"></i>
                                        <div><strong class="hidden-xs">Período Selecionado</strong></div>
                                        <div><strong class="visible-xs-block">Período</strong></div>
                                        <div class="text-primary" style="font-size: 16px; font-weight: bold;">
                                            {{vm.totalPeriodoSelecionado | currency:"R$ "}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="alert alert-warning" style="margin-bottom: 10px; padding: 10px;">
                                    <div style="text-align: center;">
                                        <i class="fa fa-calendar-minus"></i>
                                        <div><strong class="hidden-xs">Esta Semana</strong></div>
                                        <div><strong class="visible-xs-block">Semana</strong></div>
                                        <div class="text-warning" style="font-size: 16px; font-weight: bold;">
                                            {{vm.totalSemana | currency:"R$ "}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="alert alert-success" style="margin-bottom: 10px; padding: 10px;">
                                    <div style="text-align: center;">
                                        <i class="fa fa-calendar"></i>
                                        <div><strong class="hidden-xs">Este Mês</strong></div>
                                        <div><strong class="visible-xs-block">Mês</strong></div>
                                        <div class="text-success" style="font-size: 16px; font-weight: bold;">
                                            {{vm.totalMes | currency:"R$ "}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="alert alert-danger" style="margin-bottom: 10px; padding: 10px;">
                                    <div style="text-align: center;">
                                        <i class="fa fa-calculator"></i>
                                        <div><strong class="hidden-xs">Total Geral</strong></div>
                                        <div><strong class="visible-xs-block">Total</strong></div>
                                        <div class="text-danger" style="font-size: 16px; font-weight: bold;">
                                            {{vm.totalPagamentos | currency:"R$ "}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 25px 0;"></div>

                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid"
                            style="width: 100%; overflow-x: auto;" ui-grid-pinning ui-grid-save-state ui-grid-pagination
                            ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                        </div>

                        <!-- Tabela para exportação (oculta) -->
                        <div id="exportable-xls-pendencias">
                            <table style="font-family:Arial, Helvetica, sans-serif" ng-show="false" id="exportable-pendencias"
                                   class="table table-bordered" width="100%">
                                <thead>
                                    <tr>
                                        <th>Código Pagamento</th>
                                        <th>Código Viagem</th>
                                        <th>PagamentoExternoId</th>
                                        <th>ViagemExternoId</th>
                                        <th>Status</th>
                                        <th>Status Antecipação</th>
                                        <th>Valor</th>
                                        <th>Descrição</th>
                                        <th>CPF/CNPJ Proprietário</th>
                                        <th>Nome Proprietário</th>
                                        <th>Razão Social Empresa</th>
                                        <th>CNPJ Empresa</th>
                                        <th>Data Previsão Pagamento</th>
                                        <th>Data Baixa Parcela</th>
                                        <th>Data Cadastro Antecipação</th>
                                        <th>Data Alteração Antecipação</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="item in vm.dadosRelatorio">
                                        <td>{{item.id}}</td>
                                        <td>{{item.viagemId}}</td>
                                        <td>{{item.pagamentoExternoId}}</td>
                                        <td>{{item.viagemExternoId}}</td>
                                        <td>{{item.statusDescricao}}</td>
                                        <td>{{item.statusAntecipacaoDescricao}}</td>
                                        <td>{{item.valor}}</td>
                                        <td>{{item.antecipacaoMotivo}}</td>
                                        <td>{{vm.formatarCpfCnpj(item.cpfCnpjProprietario)}}</td>
                                        <td>{{item.nomeProprietario}}</td>
                                        <td>{{item.razaoSocialEmpresa}}</td>
                                        <td>{{vm.formatarCpfCnpj(item.cnpjEmpresa)}}</td>
                                        <td>{{item.dataPrevisaoPagamento}}</td>
                                        <td>{{item.dataBaixa}}</td>
                                        <td>{{item.dataCadastroAntecipacao}}</td>
                                        <td>{{item.dataAlteracaoAntecipacao}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                    </div>


                </div>
            </div>
        </div>
    </div>
</div>
</div>