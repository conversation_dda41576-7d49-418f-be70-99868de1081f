﻿(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('CentralNotificacaoController', CentralNotificacaoController);

        CentralNotificacaoController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert'
    ];

    function CentralNotificacaoController(
        BaseService, 
        $rootScope, 
        toastr, 
        $scope, 
        PersistentDataService, 
        $timeout, 
        $state, 
        PERFIL_ADMINISTRADOR,
        SweetAlert) {
        var vm = this;

        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Central de notificações' }];
        vm.pagamentosRelatorio = [];
        vm.desabilitarBtnRelatorio = false;
        vm.empresaConsulta = 0;

        vm.consultarDadosRelatorio = function (extensao) {
            if (extensao === 1)
                exportarEmExcel();

            if (extensao === 2)
                exportarEmPdf();
        };

        vm.atualizaTelaFrete = function () {
            vm.gridFreteOptions.dataSource.refresh();
        }

        vm.atualizaTelaValePedagio = function () {
            vm.gridValePedagioOptions.dataSource.refresh();
        }

        vm.novo = function () {
            toastr.info('Função indisponível no momento!');
        }

        vm.dataFrete = {
            startDate: moment().add(-7, 'days'),
            endDate: moment()
        };

        vm.dataValePedagio = {
            startDate: moment().add(-7, 'days'),
            endDate: moment()
        };

        vm.dateOptions = {
            timePicker: false,
            timePicker24Hour: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        if ($rootScope.usuarioLogado.empresaId == null || $rootScope.usuarioLogado.empresaId == undefined) {
            vm.usuAdm = true;
        } else {
            vm.usuAdm = null;
        }

        vm.perfil = vm.usuAdm ? 1 : 0;

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        }; 

        $scope.$watch('vm.consultaEmpresa.selectedValue', function (newValue) {
            vm.empresaConsulta = vm.consultaEmpresa.selectedValue;
        });

        vm.gridFreteOptions = {
            data: [],
            enableFiltering: true,
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridFreteOptions"),
            urlRelatorio: "PainelPagamento/DadosRelatorioGridPagamentos",
            params: function () {
                return {
                    dataInicial: vm.dataFrete.startDate.format('DD/MM/YYYY').toString(),
                    dataFinal: vm.dataFrete.endDate.format('DD/MM/YYYY').toString(),
                    perfil: vm.perfil,
                    empresaId: vm.empresaConsulta
                }
            },
            dataSource: {
                autoBind: false,
                url: "CentralNotificacoes/ConsultarGridCentralNotificacoes",
                params: function () {
                    return {
                        dataInicial: vm.dataFrete.startDate.format('DD/MM/YYYY').toString(),
                        dataFinal: vm.dataFrete.endDate.format('DD/MM/YYYY').toString(),
                        perfil: vm.perfil,
                        EmpresaId: vm.empresaConsulta
                    }
                }
            },
            columnDefs: [{
                name: 'Ações',
                width: '5%',
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                    <button tooltip-placement="right" uib-tooltip="Visualizar"\
                        type="button" ui-sref="central-notificacoes.central-notificacoes-crud({link: row.entity.id})"\
                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                        <i class="fa fa-eye"></i>\
                    </button>\
                    </div>\
                </div>'
            },
            {
                name: 'Status',
                displayName: 'Status',
                width: '*',
                field: 'status',
                serverField: 'pagamentoEvento.status',
                enableFiltering: true,
                enum: true,
                enumTipo: 'EStatusNotificacoes',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.status === 0"> Fechada </p>\
                                        <p ng-show="row.entity.status === 1"> Aberta </p>\
                                        <p ng-show="row.entity.status === 2"> Processando </p>\
                                        <p ng-show="row.entity.status === 3"> Erro </p>\
                                        <p ng-show="row.entity.status === 4"> Cancelado </p>\
                                        <p ng-show="row.entity.status === 5"> Processando </p>\
                                        <p ng-show="row.entity.status === 6"> Não Executado </p>\
                                   </div>'
            },
            {
                name: 'Forma Pagamento',
                displayName: 'Forma de pagamento',
                width: '*',
                field: 'formaPagamento',
                serverField: 'pagamentoEvento.formaPagamento',
                enableFiltering: true,
                enum: true,
                enumTipo: 'EFormaPagamentoEvento',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.formaPagamento === 1"> Depósito </p>\
                                        <p ng-show="row.entity.formaPagamento === 4"> Pix </p>\
                                   </div>'
            },
            {
                name: 'CodigoExternoEvento',
                displayName: 'Cód pagamento externo',
                width: '*',
                field: 'pagamentoExternoId',
                serverField: 'pagamentoEvento.pagamentoExternoId',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'ID Viagem',
                displayName: 'Viagem ID',
                width: '*',
                field: 'viagemId',
                serverField: 'pagamentoEvento.viagemId',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'CIOT',
                displayName: 'CIOT',
                width: '*',
                field: 'ciot',
                serverField: 'PagamentoEvento.Viagem.Ciot',
                type: 'text',
                enableFiltering: true
            },
            {
                name: 'ID Transação',
                displayName: 'Código transação',
                width: '*',
                field: 'pagamentoEventoId',
                serverField: 'pagamentoEventoId',
                enableFiltering: true
            },
            {
                name: 'Data/hora da transação',
                displayName: 'Data/hora da transação',
                width: '*',
                field: 'dataAlteracao',
                serverField: 'pagamentoEvento.dataAlteracao',
                enableFiltering: false
            },
            {
                name: 'FilialExternoId',
                displayName: 'Filial externo Id',
                width: '*',
                field: 'filialExternoId',
                serverField: 'pagamentoEvento.viagem.filialExternoId',
                enableFiltering: true
            },
            {
                name: 'EmpresaNome',
                displayName: 'Empresa',
                width: '*',
                field: 'empresaNome',
                serverField: 'pagamentoEvento.viagem.empresa.razaoSocial',
                enableFiltering: true
            },
            {
                name: 'Contratado',
                displayName: 'Contratado',
                width: '*',
                field: 'nomeProprietario',
                serverField: 'pagamentoEvento.viagem.portadorProprietario.nome',
                enableFiltering: true
            },
            {
                name: 'CPF/CNPJ Contratado',
                displayName: 'CPF/CNPJ Contratado',
                width: '*',
                field: 'cpfcnpjProprietario',
                serverField: 'pagamentoEvento.viagem.portadorProprietario.cpfCnpj',
                enableFiltering: true
            },
            {
                name: 'Motorista',
                displayName: 'Motorista',
                width: '*',
                field: 'nomeMotorista',
                serverField: 'pagamentoEvento.viagem.portadorMotorista.nome',
                enableFiltering: true
            },
            {
                name: 'CPF/CNPJ Motorista',
                displayName: 'CPF/CNPJ Motorista',
                width: '*',
                field: 'cpfcnpjMotorista',
                serverField: 'pagamentoEvento.viagem.portadorMotorista.cpfCnpj',
                enableFiltering: true
            },
            {
                name: 'Evento',
                displayName: 'Evento',
                width: '*',
                field: 'tipo',
                serverField: 'pagamentoEvento.tipo',
                enableFiltering: true,
                enum: true,
                enumTipo: 'ETipoEvento',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.tipo === 0"> Adiantamento </p>\
                                        <p ng-show="row.entity.tipo === 1"> Saldo </p>\
                                        <p ng-show="row.entity.tipo === 2"> Complemento </p>\
                                        <p ng-show="row.entity.tipo === 3"> Avulso </p>\
                                        <p ng-show="row.entity.tipo === 4"> Tarifa ANTT </p>\
                                        <p ng-show="row.entity.tipo === 5"> Cancelamento </p>\
                                   </div>'
            },
            {
                name: 'Valor',
                displayName: 'Valor',
                width: '*',
                field: 'valor',
                serverField: 'pagamentoEvento.valor',
                enableFiltering: false
            },
            {
                name: 'ValorTransferenciaMotorista',
                displayName: 'Valor para Motorista',
                width: '*',
                field: 'valorTransferenciaMotorista',
                serverField: 'pagamentoEvento.valorTransferenciaMotorista',
                enableFiltering: false
            },
            {
                name: 'Mensagem',
                displayName: 'Mensagem',
                width: '*',
                field: 'descricao',
                serverField: 'descricao',
                enableFiltering: false
            }]
        };

        vm.gridValePedagioOptions = {
            data: [],
            enableFiltering: true,
            
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridValePedagioOptions"),
            dataSource: {
                autoBind: false,
                url: "CentralNotificacoes/ConsultarGridCentralNotificacoesValePedagio",
                params: function () {
                    return {
                        dataInicial: vm.dataValePedagio.startDate.format('DD/MM/YYYY').toString(),
                        dataFinal: vm.dataValePedagio.endDate.format('DD/MM/YYYY').toString(),
                        perfil: vm.perfil,
                        EmpresaId: vm.empresaConsulta
                    }
                }
            },
            columnDefs: [{
                name: 'Ações',
                width: '5%',
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                    <button tooltip-placement="right" uib-tooltip="Visualizar"\
                        type="button" ui-sref="central-notificacoes.central-notificacoes-vale-pedagio-crud({link: row.entity.id})"\
                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                        <i class="fa fa-eye"></i>\
                    </button>\
                    </div>\
                </div>'
            },
            {
                name: 'Id',
                displayName: 'Código',
                field: 'id',
                type: 'number',
                width: '*',
                
                enableFiltering: true
            },
            {
                name: 'DataCadastro',
                displayName: 'Data de Cadastro',
                field: 'dataCadastro',
                width: '*',
                enableFiltering: false
            }, 
            {
                name: 'Descricao',
                displayName: 'Descrição',
                width: '*',
                field: 'descricao',
                enableFiltering: true
            },
            {
                name: 'EmpresaId',
                displayName: 'Empresa',
                width: 150,
                field: 'empresaId',
                serverField: 'pedagio.empresaId',
                enableFiltering: true
            },
            {
                name: 'Status',
                displayName: 'Status',
                field: 'status',
                width: '*',
                enableFiltering: true,
                enum: true,
                enumTipo: 'EStatusPagamentoPedagio'
            }]
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: '*'
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        };
    }
})();