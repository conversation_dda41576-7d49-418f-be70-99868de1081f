(function () {
    'use strict';

    angular.module('bbcWeb.pagamentos-antecipados.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider
            .state('pagamentos-antecipados', {
                abstract: true,
                url: "/pagamentos-antecipados",
                templateUrl: "app/layout/content.html"
            })
            .state('pagamentos-antecipados.index', {
                url: '/index',
                templateUrl: 'app/entities/pagamentos-antecipados/pagamentos-antecipados.html'
            })
            .state('pagamentos-antecipados.detalhes-pagamento', {
                url: '/detalhes-pagamento/:pagamentoId/:viagemId',
                templateUrl: 'app/entities/pagamentos-antecipados/abas/detalhes-pagamento.html'
            })
            .state('pagamentos-antecipados.detalhes-transacao', {
                url: '/detalhes-transacao/:transacaoId/:pagamentoId/:viagemId',
                templateUrl: 'app/entities/pagamentos-antecipados/abas/detalhes-transacao.html'
            });
    }
})();
