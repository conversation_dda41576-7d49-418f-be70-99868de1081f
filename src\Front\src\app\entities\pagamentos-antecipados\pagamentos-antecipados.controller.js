(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PagamentosAntecipadosController', PagamentosAntecipadosController);

    PagamentosAntecipadosController.inject = [
        'BaseService',
        '$rootScope',
        '$window',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        '$uibModal',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert',
        'uiGridConstants'
    ];

    function PagamentosAntecipadosController(
        BaseService, 
        $rootScope, 
        $window,
        toastr, 
        $scope,
        PersistentDataService, 
        $timeout, 
        $state,
        $uibModal,
        PERFIL_ADMINISTRADOR,
        SweetAlert,
        uiGridConstants) {
        
        var vm = this;
        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Pagamentos Antecipados' }];
        vm.pagamentosRelatorio = [];
        vm.desabilitarBtnRelatorio = false;
        vm.primeiraConsulta = true;
        vm.totalPagamentosAno = 0;
        vm.totalSemana = 0;
        vm.totalMes = 0;
        vm.totalPeriodoSelecionado = 0;
        vm.filtrosExpandidos = true;
        vm.semDados = false;

        var alertaFechadoStorage = localStorage.getItem('pagamentosAntecipadosAlertaFechado');
        vm.alertaFechado = alertaFechadoStorage === 'true';

        if ($rootScope.usuarioLogado.empresaId == null || $rootScope.usuarioLogado.empresaId == undefined) {
            vm.usuAdm = true;
        } else {
            vm.usuAdm = null;
        }

        // Filtros
        vm.filtros = {
            periodo: {
                startDate: moment().startOf('month'),
                endDate: moment().endOf('month')
            },
            empresaId: null,
            cpfCnpjProprietario: ''
        };




        vm.dateOptions = {
            timePicker: false,
            timePicker24Hour: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Hoje': [moment(), moment()],
                'Ontem': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Últimos 7 dias': [moment().subtract(6, 'days'), moment()],
                'Últimos 30 dias': [moment().subtract(29, 'days'), moment()],
                'Este mês': [moment().startOf('month'), moment().endOf('month')],
                'Mês passado': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        };


        

        // Configuração da consulta de empresa
        vm.consultaEmpresa = {
            url: 'Empresa/ConsultarEmpresasParaConsultaPadrao',
            columns: [
                { name: 'id', displayName: 'Código', width: 100 },
                { name: 'razaoSocial', displayName: 'Razão Social', width: 300 },
                { name: 'cnpj', displayName: 'CNPJ', width: 150 }
            ],
            title: 'Consulta de Empresas'
        };

        // Grid Options
        vm.gridOptions = {
            data: [],
            enableFiltering: true,
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            enableHorizontalScrollbar: 1, // Habilita scroll horizontal
            enableVerticalScrollbar: 1,   // Habilita scroll vertical
            urlRelatorio: "Viagem/ConsultarGridPagamentosAntecipados",
            dataSource: {
                url: "Viagem/ConsultarGridPagamentosAntecipados",
                params: function() {
                    var inicio = vm.filtros.periodo.startDate.toDate();
                    var fim = vm.filtros.periodo.endDate.toDate();
                    return {
                        dataInicial: new Date(inicio.getTime() - (inicio.getTimezoneOffset() * 60000)).toJSON(),
                        dataFinal: new Date(fim.getTime() - (fim.getTimezoneOffset() * 60000)).toJSON(),
                        empresaId: vm.filtros.empresaId,
                        cpfCnpjProprietario: vm.filtros.cpfCnpjProprietario
                    };
                },
                consultarDadosRelatorio: function(callback) {
                    var params = this.params();
                    BaseService.get("Viagem/ConsultarGridPagamentosAntecipados", params).then(callback);
                },
                events: {
                    onDataBound: function(data) {
                        // Este evento sempre executa, mesmo quando não há dados
                        console.log('🔥 onDataBound executado! Data:', data);

                        // Fazer uma nova requisição para pegar os totais quando não há dados na grid
                        if (!data || data.length === 0) {
                            vm.semDados = true;
                            vm.totalPeriodoSelecionado = 0;
                        }else {
                            vm.semDados = false;
                        }
                    }
                }
            },
            callBack: function(response) {
                console.log('🔥 CALLBACK EXECUTADO! Response completa:', response);
                if (response.data.items.length != 0) {
                    vm.totalPagamentosAno = response.data.totalPagamentosAno || 0;
                    vm.totalSemana = response.data.totalSemana || 0;
                    vm.totalMes = response.data.totalMes || 0;
                    vm.totalPeriodoSelecionado = response.data.totalPeriodoSelecionado || 0;

                    // Verificar se há dados na grid
                    vm.semDados = !response.data.items || !Array.isArray(response.data.items) || response.data.items.length === 0;

                    console.log('=== TOTAIS DO BACKEND ===');
                    console.log('Response data items:', response.data.items);
                    console.log('Items é array?:', Array.isArray(response.data.items));
                    console.log('Items length:', response.data.items ? response.data.items.length : 'undefined');
                    console.log('Items real length:', response.data.items && response.data.items.length);
                    console.log('Total geral:', vm.totalPagamentosAno);
                    console.log('Total semana atual:', vm.totalSemana);
                    console.log('Total mês atual:', vm.totalMes);
                    console.log('Total período selecionado:', vm.totalPeriodoSelecionado);
                    console.log('Sem dados (vm.semDados):', vm.semDados);
                    console.log('========================');
                } else {
                    vm.semDados = true;
                    // Zerar totais quando não há resposta válida
                    vm.totalPagamentosAno = 0;
                    vm.totalSemana = 0;
                    vm.totalMes = 0;
                    vm.totalPeriodoSelecionado = 0;
                }
            },
            columnDefs: [
                {
                    name: 'acoes',
                    displayName: 'Ações',
                    cellTemplate: '<div class="ui-grid-cell-contents text-center">' +
                        '<button class="btn btn-xs btn-info" ng-click="grid.appScope.vm.visualizarDetalhes(row.entity)" title="Visualizar Detalhes">' +
                        '<i class="fa fa-eye"></i>' +
                        '</button>' +
                        '</div>',
                    width: 80,
                    enableSorting: false,
                    enableFiltering: false
                },
                { name: 'id', displayName: 'Código Pagamento', width: 120 },
                { name: 'viagemId', displayName: 'Código Viagem', width: 120 },
                { name: 'pagamentoExternoId', displayName: 'PagamentoExternoId', width: 150 },
                { name: 'viagemExternoId', displayName: 'ViagemExternoId', width: 150 },
                {
                    name: 'status',
                    displayName: 'Status',
                    cellTemplate: '<div class="ui-grid-cell-contents">' +
                        '<span ng-class="{' +
                        '\'label label-success\': row.entity.statusDescricao === \'Fechado\', ' +
                        '\'label label-warning\': row.entity.statusDescricao === \'Aberto\', ' +
                        '\'label label-info\': row.entity.statusDescricao === \'Pendente\', ' +
                        '\'label label-danger\': row.entity.statusDescricao === \'Erro\', ' +
                        '\'label label-default\': row.entity.statusDescricao === \'Cancelado\', ' +
                        '\'label label-primary\': row.entity.statusDescricao === \'Processando\', ' +
                        '\'label label-inverse\': row.entity.statusDescricao === \'Não Executado\'' +
                        '}">' +
                        '{{row.entity.statusDescricao}}' +
                        '</span>' +
                        '</div>',
                    width: 120
                },
                {
                    name: 'statusAntecipacaoParcelaProprietario',
                    displayName: 'Status Antecipação',
                    cellTemplate: '<div class="ui-grid-cell-contents">' +
                        '<span ng-class="{' +
                        '\'label label-primary\': row.entity.statusAntecipacaoDescricao === \'Disponivel\', ' +
                        '\'label label-info\': row.entity.statusAntecipacaoDescricao === \'AguardandoProcessamento\', ' +
                        '\'label label-success\': row.entity.statusAntecipacaoDescricao === \'Aprovado\', ' +
                        '\'label label-danger\': row.entity.statusAntecipacaoDescricao === \'Erro\', ' +
                        '\'label label-default\': row.entity.statusAntecipacaoDescricao === \'Não Disponível\'' +
                        '}">' +
                        '{{row.entity.statusAntecipacaoDescricao}}' +
                        '</span>' +
                        '</div>',
                    width: 150
                },
                { 
                    name: 'valor', 
                    displayName: 'Valor',
                    cellFilter: 'currency:"R$ "',
                    width: 120
                },
                { name: 'antecipacaoMotivo', displayName: 'Descrição', width: 200 },
                { name: 'cpfCnpjProprietario', displayName: 'CPF/CNPJ Proprietário', width: 150 },
                { name: 'nomeProprietario', displayName: 'Nome Proprietário', width: 200 },
                { name: 'razaoSocialEmpresa', displayName: 'Razão Social Empresa', width: 200 },
                { name: 'cnpjEmpresa', displayName: 'CNPJ Empresa', width: 150 },
                { name: 'dataPrevisaoPagamento', displayName: 'Data Previsão Pagamento', width: 150 },
                { name: 'dataBaixa', displayName: 'Data Baixa Parcela', width: 150 },
                { name: 'dataCadastroAntecipacao', displayName: 'Data Cadastro Antecipação', width: 180 },
                { name: 'dataAlteracaoAntecipacao', displayName: 'Data Alteração Antecipação', width: 180 }
            ]
        };

        vm.toggleFiltros = function() {
            vm.filtrosExpandidos = !vm.filtrosExpandidos;
        };

        vm.fecharAlerta = function() {
            vm.alertaFechado = true;
            localStorage.setItem('pagamentosAntecipadosAlertaFechado', 'true');
        };

        vm.limparTotais = function() {
            // Limpar totais
            vm.totalPagamentosAno = 0;
            vm.totalSemana = 0;
            vm.totalMes = 0;
            vm.totalPeriodoSelecionado = 0;

            // Limpar filtros
            vm.filtros.periodo = {
                startDate: moment().subtract(1, 'days').startOf('day'),
                endDate: moment().endOf('day')
            };
            vm.filtros.empresaId = null;
            vm.filtros.cpfCnpjProprietario = '';

            // Refazer consulta
            vm.consultar();
        };

        vm.formatarCpfCnpj = function(value) {
            if (!value) return '';
            var digits = value.replace(/\D/g, '');
            if (digits.length <= 11) {
                return digits.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
            } else {
                return digits.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
            }
        };

        vm.reexibirAlerta = function() {
            vm.alertaFechado = false;
            localStorage.removeItem('pagamentosAntecipadosAlertaFechado');
        };

        // Funções
        vm.consultar = function() {
            if (vm.gridOptions && vm.gridOptions.dataSource && vm.gridOptions.dataSource.refresh) {
                vm.gridOptions.dataSource.refresh();
            }
        };

        vm.visualizarDetalhes = function(pagamento) {
            $state.go('pagamentos-antecipados.detalhes-pagamento', {
                pagamentoId: pagamento.id,
                viagemId: pagamento.viagemId
            });
        };

        vm.exportarRelatorio = function() {
            vm.desabilitarBtnRelatorio = true;
            
            vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmExcel2("exportable-xls", "PagamentosAntecipados", true);
                    vm.desabilitarBtnRelatorio = false;
                }, 500);
            });
        };

        vm.limparFiltros = function() {
            vm.filtros = {
                periodo: {
                    startDate: moment().subtract(1, 'days').startOf('day'),
                    endDate: moment().endOf('day')
                },
                empresaId: null,
                cpfCnpjProprietario: ''
            };
            vm.consultar();
        };

        // Inicialização
        vm.consultar();
    }
})();
