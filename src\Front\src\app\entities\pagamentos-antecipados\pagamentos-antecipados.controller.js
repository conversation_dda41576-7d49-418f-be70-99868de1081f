(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PagamentosAntecipadosController', PagamentosAntecipadosController);

    PagamentosAntecipadosController.inject = [
        'BaseService',
        '$rootScope',
        '$window',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        '$uibModal',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert',
        'uiGridConstants'
    ];

    function PagamentosAntecipadosController(
        BaseService, 
        $rootScope, 
        $window,
        toastr, 
        $scope,
        PersistentDataService, 
        $timeout, 
        $state,
        $uibModal,
        PERFIL_ADMINISTRADOR,
        SweetAlert,
        uiGridConstants) {
        
        var vm = this;
        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Pagamentos Antecipados' }];
        vm.pagamentosRelatorio = [];
        vm.desabilitarBtnRelatorio = false;
        vm.primeiraConsulta = true;
        vm.totalPagamentos = 0;

        // Filtros
        vm.filtros = {
            periodo: {
                startDate: moment().subtract(1, 'days').startOf('day'),
                endDate: moment().endOf('day')
            },
            empresaId: null,
            cpfCnpjProprietario: ''
        };

        // Configurações do DateRangePicker
        vm.dateRangeOptions = {
            locale: {
                applyLabel: "Aplicar",
                cancelLabel: "Cancelar",
                fromLabel: "De",
                toLabel: "Até",
                customRangeLabel: "Personalizado",
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'],
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
                    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
                firstDay: 1
            },
            ranges: {
                'Hoje': [moment(), moment()],
                'Ontem': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Últimos 7 dias': [moment().subtract(6, 'days'), moment()],
                'Últimos 30 dias': [moment().subtract(29, 'days'), moment()],
                'Este mês': [moment().startOf('month'), moment().endOf('month')],
                'Mês passado': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        };

        // Grid Options
        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Viagem/ConsultarGridPagamentosAntecipados",
                params: function() {
                    return {
                        dataInicial: moment(vm.filtros.periodo.startDate).format('DD/MM/YYYY'),
                        dataFinal: moment(vm.filtros.periodo.endDate).format('DD/MM/YYYY'),
                        empresaId: vm.filtros.empresaId,
                        cpfCnpjProprietario: vm.filtros.cpfCnpjProprietario
                    };
                },
                onSuccess: function(response) {
                    vm.calcularTotal(response.data.items);
                }
            },
            columnDefs: [
                {
                    name: 'acoes',
                    displayName: 'Ações',
                    cellTemplate: '<div class="ui-grid-cell-contents text-center">' +
                        '<button class="btn btn-xs btn-primary" ng-click="grid.appScope.vm.visualizarDetalhes(row.entity)" title="Visualizar Detalhes">' +
                        '<i class="fa fa-eye"></i>' +
                        '</button>' +
                        '</div>',
                    width: 80,
                    enableSorting: false,
                    enableFiltering: false
                },
                { name: 'id', displayName: 'Código Pagamento', width: 120 },
                { name: 'viagemId', displayName: 'Código Viagem', width: 120 },
                { name: 'pagamentoExternoId', displayName: 'PagamentoExternoId', width: 150 },
                { name: 'viagemExternoId', displayName: 'ViagemExternoId', width: 150 },
                { 
                    name: 'status', 
                    displayName: 'Status',
                    cellTemplate: '<div class="ui-grid-cell-contents">' +
                        '<span ng-class="{\'label label-success\': row.entity.status === \'Fechado\', \'label label-warning\': row.entity.status === \'Aberto\'}">' +
                        '{{row.entity.statusDescricao}}' +
                        '</span>' +
                        '</div>',
                    width: 100
                },
                { 
                    name: 'statusAntecipacaoParcelaProprietario', 
                    displayName: 'Status Antecipação',
                    cellTemplate: '<div class="ui-grid-cell-contents">' +
                        '<span ng-class="{\'label label-info\': row.entity.statusAntecipacaoParcelaProprietario === \'AguardandoProcessamento\', ' +
                        '\'label label-success\': row.entity.statusAntecipacaoParcelaProprietario === \'Aprovado\', ' +
                        '\'label label-danger\': row.entity.statusAntecipacaoParcelaProprietario === \'Erro\'}">' +
                        '{{row.entity.statusAntecipacaoDescricao}}' +
                        '</span>' +
                        '</div>',
                    width: 150
                },
                { 
                    name: 'valor', 
                    displayName: 'Valor',
                    cellFilter: 'currency:"R$ "',
                    width: 120
                },
                { name: 'antecipacaoMotivo', displayName: 'Descrição', width: 200 },
                { name: 'cpfCnpjProprietario', displayName: 'CPF/CNPJ Proprietário', width: 150 },
                { name: 'nomeProprietario', displayName: 'Nome Proprietário', width: 200 },
                { name: 'razaoSocialEmpresa', displayName: 'Razão Social Empresa', width: 200 },
                { name: 'cnpjEmpresa', displayName: 'CNPJ Empresa', width: 150 },
                { name: 'dataPrevisaoPagamento', displayName: 'Data Previsão Pagamento', width: 150 },
                { name: 'dataBaixa', displayName: 'Data Baixa Parcela', width: 150 },
                { name: 'dataCadastroAntecipacao', displayName: 'Data Cadastro Antecipação', width: 180 },
                { name: 'dataAlteracaoAntecipacao', displayName: 'Data Alteração Antecipação', width: 180 }
            ]
        };

        // Funções
        vm.consultar = function() {
            vm.gridOptions.dataSource.refresh();
        };

        vm.calcularTotal = function(items) {
            vm.totalPagamentos = 0;
            if (items && items.length > 0) {
                vm.totalPagamentos = items.reduce(function(total, item) {
                    return total + (item.valor || 0);
                }, 0);
            }
        };

        vm.visualizarDetalhes = function(pagamento) {
            $state.go('pagamentos-antecipados.detalhes-pagamento', { 
                pagamentoId: pagamento.id,
                viagemId: pagamento.viagemId 
            });
        };

        vm.exportarRelatorio = function() {
            vm.desabilitarBtnRelatorio = true;
            
            vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmExcel2("exportable-xls", "PagamentosAntecipados", true);
                    vm.desabilitarBtnRelatorio = false;
                }, 500);
            });
        };

        vm.limparFiltros = function() {
            vm.filtros = {
                periodo: {
                    startDate: moment().subtract(1, 'days').startOf('day'),
                    endDate: moment().endOf('day')
                },
                empresaId: null,
                cpfCnpjProprietario: ''
            };
            vm.consultar();
        };

        // Inicialização
        vm.consultar();
    }
})();
