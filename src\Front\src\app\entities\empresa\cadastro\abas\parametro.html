<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">
                        <span class="text-danger mr-5" ng-show="vm.isAdmin()">*</span>Tipo emissão CIOT:
                    </label>
                    <div class="input-group col-xs-12 col-md-8">
                        <ui-select name="TipoEmissaoCiot" ng-model="vm.empresa.tipoEmissaoCiot" ats-ui-select-validator
                            validate-on="blur" ng-required="vm.isAdmin()"
                            required-message="'Tipo emissão CIOT é obrigatório'">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboTipoEmissaoCiot.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-4 control-label">
                        <span class="text-danger mr-5">*</span>Tempo entre Abastecimentos (Minutos):
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <input type="text" required ng-model="vm.empresa.tempoAbastecimento"
                            required-message="'Tempo de abastecimento é obrigatório'" maxlength="3" name="tempo"
                            class="form-control" ui-number-mask="0" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-4 control-label">
                        <span class="text-danger mr-5">*</span>Tolerância Abastecimento:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <input type="text" required ng-model="vm.empresa.valorTolerancia"
                            required-message="'Tolerância de abastecimento é obrigatório'" maxlength="14"
                            name="tolerancia" class="form-control" ui-number-mask="3" max="999999999.999" />
                    </div>
                </div>
            </div>
            <consulta-padrao-modal directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'" idname="TipoEmpresa"
                idmodel="TipoEmpresa" labelsize="'col-xs-12 col-sm-3 col-md-5 col-lg-4 control-label'"
                tabledefinition="vm.consultaTipoEmpresaModal" label="'Tipo de empresa:'"
                placeholder="'Selecione um tipo de empresa'" required-message="'Tipo de empresa é obrigatório'"
                validate-on="blur" ng-required="vm.empresa.link">
            </consulta-padrao-modal>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-4 control-label">
                        % Autonomia Inferior:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <input type="text" ng-model="vm.empresa.percentualAutonomiaInferior" maxlength="3"
                            name="autonomiaInferior" class="form-control" ui-number-mask="0" max="999" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-4 control-label">
                        % Autonomia Superior:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <input type="text" ng-model="vm.empresa.percentualAutonomiaSuperior" maxlength="3"
                            name="autonomiaSuperior" class="form-control" ui-number-mask="0" max="999" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Libera Bloqueio SPD:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.liberaBloqueioSPD"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Controla odômetro:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.controlaOdometro"
                            class="switch" ng-change="vm.ctrlOdometroChange()"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Registra CIOT:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.registraCiot"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6" ng-if=vm.empresa.controlaOdometro=="Sim">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Controla autonomia:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" aria-label="Não" id="ctrolAutonomia"
                            ng-model="vm.empresa.controlaAutonomia" class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Utiliza CIOT:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.utilizaCiot"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Permite encerramento CIOT via Painel:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não"
                            ng-model="vm.empresa.permitirEncerramentoPainelCiot" class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Controla contingência:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.controlaContingencia"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">E-mails - Notificação contingência CIOT’s:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <textarea style="resize: none;" type="text" maxlength="1500" rows="7"
                            ng-model="vm.empresa.notificacaoContingenciaCiot"
                            name="NotificacaoInternaContingenciaAutomárica"
                            class="form-control ng-pristine ng-valid ng-empty ng-touched user-success"
                            ng-keydown="vm.checkForEmailEnd($event)"> </textarea>
                    </div>
                </div>
            </div>
        </div>
        <hr-label class="mb-15" dark="true" title="'Controle de pagamentos de abastecimento'"></hr-label>
        <br />
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Data:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch
                            ng-change="vm.empresa.debitoPrazo == true ? vm.empresa.debitoProtocolo = false : vm.empresa.debitoProtocolo = vm.empresa.debitoProtocolo"
                            on-label="Sim" off-label="Não" ng-model="vm.empresa.debitoPrazo"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-4 control-label">
                        Prazo:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <input type="text" ng-required="vm.empresa.debitoPrazo == true" ng-model="vm.empresa.prazo"
                            maxlength="3" name="Prazo" class="form-control"
                            ng-disabled="vm.empresa.debitoPrazo == false" ui-number-mask="0" max="999" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Débito no protocolo de NF:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch
                            ng-change="vm.empresa.debitoProtocolo == true ? vm.empresa.debitoPrazo = false : vm.empresa.debitoPrazo = vm.empresa.debitoPrazo"
                            on-label="Sim" off-label="Não" ng-model="vm.empresa.debitoProtocolo"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <hr-label class="mb-15" dark="true" title="'Controle de pagamentos de frete'"></hr-label>
        <br />
        <div class="row" ng-show="!vm.isNew()">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-12 col-md-4 col-lg-4 control-label">Conta pagamento Frete:</label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <ui-select name="contaFrete" ng-model="vm.empresa.contaFrete"
                            ng-disabled="vm.isFieldContaDisabled" ats-ui-select-validator>
                            <ui-select-match>
                                <span style="color: #5E5E5E !important;">{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.contas | filter: $select.search">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Realizar pagamento via Pix para o Recebedor
                        Autorizado:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.recebedorAutorizado"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Status de reprocessamento pagamento de frete</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não"
                            ng-model="vm.empresa.statusReprocessamentoPagamentoFrete" class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Utilizar versão V2 do pagamento:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.versaoIntegracaoViagem"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <hr-label class="mb-15" dark="true" title="'Controle de pagamentos de pedágio'"></hr-label>

        <br />
        <div class="row" ng-show="!vm.isNew()">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-12 col-md-4 col-lg-4 control-label">Conta pagamento vale
                        pedágio:</label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <ui-select name="contaValePedagio" ng-model="vm.empresa.contaValePedagio"
                            ng-disabled="vm.isFieldContaDisabled" ats-ui-select-validator>
                            <ui-select-match>
                                <span style="color: #5E5E5E !important;">{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.contas | filter: $select.search">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>

        <br />

        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Pagamento vale pedágio:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.permitirPagamentoValePedagio"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Status de reprocessamento pagamento de vale
                        pedágio:</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não"
                            ng-model="vm.empresa.habilitaReprocessamentoValePedagio" class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <hr-label class="mb-15" dark="true" title="'Controle de painel de saldo'"></hr-label>
        <br />
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Permite visualizar telão</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.habilitaPainelSaldo"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Valor Adiantamento</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <input type="text" ui-money-mask="2" class="form-control"
                            ng-model="vm.empresa.valorAdiantamentoBbc">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>