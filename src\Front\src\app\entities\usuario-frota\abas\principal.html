<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">Código:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-3 col-lg-3">
                        <input type="text" ng-model="vm.portador.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
                
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Nome:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-disabled="vm.portador.status === 'Cancelado'" maxlength="200" name="Nome" ng-model="vm.portador.nome" class="form-control"
                            validate-on="blur" required-message="'Nome é obrigatório'" required />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>{{vm.labelCpfCnpj}}:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-disabled="vm.portador.status === 'Cancelado' || !vm.isNew()" ui-mask="{{vm.mascaraCpfCnpj}}" ng-disabled="!vm.isNew()" required
                            ng-model="vm.portador.cpfCnpj" name="{{vm.labelCpfCnpj}}"
                            class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Celular:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-disabled="vm.portador.status === 'Cancelado'" name="Celular" class="form-control" ng-model="vm.portador.celular"
                            ui-br-phone-number validate-on="blur" required-message="'Celular é obrigatório'" required />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" >

            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>CNH:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-disabled="vm.portador.status === 'Cancelado'" ui-mask="{{vm.mascaraCNH}}" required
                            ng-model="vm.portador.cnh" name="{{vm.labelCpfCnpj}}"
                            class="form-control" />
                    </div>
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"><span
                            class="text-danger mr-5">*</span>Data Emissão CNH:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <p class="input-group">
                            <input type="text" ng-disabled="vm.portador.status === 'Cancelado'" ng-click="vm.datePickerOpenemissao = !vm.datePickerOpenemissao"
                                class="form-control" datepicker-options="vm.optionsDatePickerEmissaoCNH" name="emisaoid" current-text="Hoje" clear-text="Limpar"
                                close-text="Fechar" uib-datepicker-popup="dd/MM/yyyy" datepicker-append-to-body="true"
                                ng-model="vm.portador.dataEmissaoCNH" is-open="vm.datePickerOpenEmissao"
                                required-message="'Data Emissão CNH é obrigatório'"
                                required  />
                            <span class="input-group-btn">
                                <button  ng-disabled="vm.portador.status === 'Cancelado'" type="button" class="btn btn-default"
                                    ng-click="vm.datePickerOpenEmissao = !vm.datePickerOpenEmissao">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </span>
                        </p>
                    </div>
                </div>
            </div>
            
        </div>

        <div class="row">

            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"><span
                            class="text-danger mr-5">*</span>Data Vencimento CNH:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <p class="input-group">
                            <input type="text" ng-disabled="vm.portador.status === 'Cancelado'" ng-click="vm.datePickerOpennasc = !vm.datePickerOpennasc"
                                class="form-control" datepicker-options="vm.optionsDatePickerValidadeCNH" name="nascimento" current-text="Hoje" clear-text="Limpar"
                                close-text="Fechar" uib-datepicker-popup="dd/MM/yyyy" datepicker-append-to-body="true"
                                ng-model="vm.portador.dataVencimentoCNH" is-open="vm.datePickerOpennasc"
                                required-message="'Data Vencimento é obrigatório'"
                                required />
                            <span class="input-group-btn">
                                <button ng-disabled="vm.portador.status === 'Cancelado'" type="button" class="btn btn-default"
                                    ng-click="vm.datePickerOpennasc = !vm.datePickerOpennasc">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </span>
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="col-xs-12 col-md-3 control-label">
                        <span class="text-danger mr-5">*</span>E-mail:
                    </label>
                    <div class="input-group col-xs-12 col-md-9">
                        <input type="email" name="Email" maxlength="200" ng-disabled="vm.portador.status === 'Cancelado'"
                            class="form-control" validate-on="blur" ng-model="vm.portador.email"
                            required-message="'E-mail é obrigatório'" required
                            invalid-message="'E-mail é inválido'"/>
                        <!--<small>Caso informado mais de um e-mail, separar por ponto e vírgula.</small>-->
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <consulta-padrao-modal ng-disabled="!vm.isAdmin() || vm.portador.status === 'Cancelado'"
                tabledefinition="vm.consultaEmpresa" label="'Empresa:'" idname="Empresa"
                placeholder="'Selecione uma Empresa'" required-message="'Empresa é obrigatório'"
                ng-required="vm.isAdmin()" directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
            </consulta-padrao-modal>
        </div>
    </div>
</div>