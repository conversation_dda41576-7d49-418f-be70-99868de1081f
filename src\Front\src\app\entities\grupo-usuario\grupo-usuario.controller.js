(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('GrupoUsuarioController', GrupoUsuarioController);

    GrupoUsuarioController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function GrupoUsuarioController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Grupo de usuário'
        }];
        
        vm.sistema = null;

        vm.combosSistema = [
            {data:0,label:'BBC Controle'},
			{data:1,label:'Rede BBC Controle'},
            {data:null,label:'Todos'}
        ];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };
        
        vm.gridOptions = {
            data: [],  
            onRegisterApi: function (gridApi) {
                BaseService.dataGrid.defaultOnRegisterApi(gridApi);
                vm.gridApi = gridApi;

                const isAdmin = $rootScope.usuarioLogado.administrador === true;
                const colunasParaMostrar = ['Empresa', 'Posto'];

                colunasParaMostrar.forEach(nomeColuna => {
                    const coluna = vm.gridOptions.columnDefs.find(c => c.name === nomeColuna);
                    if (coluna) {
                        coluna.visible = isAdmin;
                    }
                });

                vm.gridApi.core.notifyDataChange(uiGridConstants.dataChange.COLUMN);
            },
            dataSource: {
                url: "GrupoUsuario/ConsultarGridGrupoUsuario",
                params: function () {
                    return {
                        sistema: vm.sistema
                    };
                },
            },
            columnDefs: [{
                name: 'Ações',
                width: 70,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="grupo-usuario.grupo-usuario-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                type : 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Descrição',
                width: '*',
                minWidth: 180,
                field: 'descricao'
            },
            {
                name: 'Sistema',
                width: '*',
                minWidth: 180,
                field: 'sistema',
                enum: true,
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                    <p ng-show="row.entity.sistema === 1"> Rede BBC Controle </p>\
                                    <p ng-show="row.entity.sistema === 0"> BBC Controle </p>\
                            </div>'
            },
            {
                name: 'Empresa',
                width: '*',
                minWidth: 180,
                field: 'nomeFantasiaEmpresa',
                serverField: 'Empresa.NomeFantasia',
                visible: vm.isAdmin()
            },
            {
                name: 'Posto',
                width: '*',
                minWidth: 180,
                field: 'nomeFantasiaPosto',
                serverField: 'Posto.NomeFantasia',
                visible: vm.isAdmin()
            }]
        };
        vm.gridOptions.minimumColumnSize = 100;
       
        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('GrupoUsuarioController', vm, "Grupos de usuário", "GrupoUsuarioCrudController", "grupo-usuario.index");
        });

        var selfScope = PersistentDataService.get('GrupoUsuarioController');
        var filho = PersistentDataService.get('GrupoUsuarioCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('grupo-usuario.grupo-usuario-crud', {
                    link: filho.data.grupoUsuario.IdGrupoUsuario > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();