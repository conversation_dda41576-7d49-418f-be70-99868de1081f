(function () {
    'use strict';

    angular.module('bbcWeb').controller('ClientSecretConsultaFreteCrudController', ClientSecretConsultaFreteCrudController);

    ClientSecretConsultaFreteCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function ClientSecretConsultaFreteCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;

        vm.isNew = function () {
            return $stateParams.link === 'novo';
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.isSaving = false;

        vm.voltar = function () {
            $state.go('client-secret-consulta-frete.index');
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving) return;

            vm.isSaving = true;

            var saveClientSecretConsultaFrete = {
                login: vm.clientSecretConsultaFrete.login,
                senha: vm.clientSecretConsultaFrete.senha,
                clientSecret: vm.clientSecretConsultaFrete.clientSecret,
                descricao: vm.clientSecretConsultaFrete.descricao,
                id: vm.clientSecretConsultaFrete.id === "Auto" ? 0 : vm.clientSecretConsultaFrete.id
            }

            if (!vm.isNew() && !vm.clientSecretConsultaFrete.senha) {
                delete saveClientSecretConsultaFrete.senha; // Não alterar senha se não informada
            }

            if (vm.isNew()) {
                // Para novo registro, abre a modal para mostrar o client secret
                abrirModal(saveClientSecretConsultaFrete);
            } else {
                // Para edição, apenas salva e volta para a listagem
                salvarEdicao(saveClientSecretConsultaFrete);
            }
        };

        function salvarEdicao(saveClientSecretConsultaFrete) {
            BaseService.post('ClientSecretAdm', 'SaveClientSecretAdm', saveClientSecretConsultaFrete).then(function (response) {
                vm.isSaving = false;
                if (response.success) {
                    toastr.success(response.message);
                    vm.voltar(); // Volta para a listagem
                } else {
                    toastr.error(response.message);
                }
            }).catch(function (error) {
                vm.isSaving = false;
                toastr.error('Erro ao salvar as alterações');
            });
        }

        function abrirModal(saveClientSecretConsultaFrete) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/client-secret-consulta-frete/modal/client-secret-consulta-frete-modal.html',
                controller: function ($uibModalInstance, toastr, BaseService, saveClientSecretConsultaFrete, $rootScope) {
                    var vm = this;
                    vm.carregandoEdit = true;

                    BaseService.post('ClientSecretAdm', 'SaveClientSecretAdm', saveClientSecretConsultaFrete).then(function (response) {
                        vm.carregandoEdit = false;
                        if (response.success) {
                            toastr.success(response.message);
                            vm.clientSecretConsultaFrete = response.data
                        } else {
                            $uibModalInstance.close();
                            toastr.error(response.message);
                        }

                    });

                    vm.closeModal = function () {
                        $uibModalInstance.close();
                        $state.go('client-secret-consulta-frete.index');
                    };

                    vm.copiar = function () {
                        if (navigator.clipboard) {
                            navigator.clipboard.writeText(vm.clientSecretConsultaFrete).then(function () {
                                toastr.success('Client Secret copiado para a área de transferência!');
                            });
                        } else {
                            // Fallback para navegadores mais antigos
                            var textArea = document.createElement("textarea");
                            textArea.value = vm.clientSecretConsultaFrete;
                            document.body.appendChild(textArea);
                            textArea.focus();
                            textArea.select();
                            try {
                                document.execCommand('copy');
                                toastr.success('Client Secret copiado para a área de transferência!');
                            } catch (err) {
                                toastr.error('Erro ao copiar Client Secret');
                            }
                            document.body.removeChild(textArea);
                        }
                    };
                },
                controllerAs: 'vm',
                resolve: {
                    saveClientSecretConsultaFrete: function () {
                        return saveClientSecretConsultaFrete;
                    }
                }
            }).result.then(function () {
                vm.isSaving = false;
            }, function () {
                vm.isSaving = false;
            });
        }

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Client Secret Consulta Frete',
            link: 'client-secret-consulta-frete.index'
        }, {
            name: $stateParams.link === 'novo' ? 'Novo' : 'Editar'
        }];

        vm.clientSecretConsultaFrete = {
            ativo: 1
        };

        // Carregar dados se for edição
        if (!vm.isNew()) {
            BaseService.get('ClientSecretAdm', 'ConsultarPorId', { idAuthClientSecret: $stateParams.link }).then(function (response) {
                if (response.success) {
                    vm.clientSecretConsultaFrete = response.data;
                    vm.clientSecretConsultaFrete.senha = ''; // Não carregar senha por segurança
                } else {
                    toastr.error(response.message);
                    vm.voltar();
                }
            });
        }
    }
})();
