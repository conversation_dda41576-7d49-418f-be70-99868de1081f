(function () {
    'use strict';

    angular.module('bbcWeb').factory('DefaultsService', DefaultsService);

    DefaultsService.$inject = ['$http', 'URL_SERVER_DEV', '$window'];

    function DefaultsService($http, URL_SERVER_DEV, $window) {
        var fn = {

            multiDomain: function () {
                var idEmpBase = $window.localStorage.getItem('idEmpresaBase');

                if (angular.isDefined(idEmpBase) && idEmpBase !== null) {
                    var fixedType = idEmpBase.toFixedType();
                    // Definitivamente tem idEmpresaBase...
                    if (fixedType !== null && fixedType > 0) return true;
                }
                return false
            },

            getDefaultCompany: function () {
                var idEmpresaBase = $window.localStorage.getItem('idEmpresaBase').toFixedType();
                if (angular.isDefined(idEmpresaBase) && idEmpresaBase !== '' && idEmpresaBase !== null)
                    return idEmpresaBase;

                return null;
            },

            consultaEmpresa: function () {
                var applyMultiDomainCompany = false;
                var idEmpBase = $window.localStorage.getItem('idEmpresaBase');

                if (angular.isDefined(idEmpBase) && idEmpBase !== null) {
                    var fixedType = idEmpBase.toFixedType();
                    // Definitivamente tem idEmpresaBase...
                    if (fixedType !== null && fixedType > 0) applyMultiDomainCompany = true;
                }

                return {
                    columnDefs: [{
                        name: 'Código',
                        field: 'IdEmpresa',
                        width: '6%',
                        primaryKey: true
                    }, {
                        name: 'Razão Social',
                        field: 'RazaoSocial',
                        enableGrouping: true,
                        width: '40%'
                    }, {
                        name: 'CNPJ',
                        field: 'CNPJ',
                        enableGrouping: true,
                        width: '40%'
                    }],
                    desiredValue: 'IdEmpresa',
                    desiredText: 'RazaoSocial',
                    url: 'EmpresaAts/ConsultarGrid',
                    applyMultiDomainCompany: applyMultiDomainCompany
                }
            },
            consultaHardware: function (paramsMethod) {
                return {
                    columnDefs: [{
                        name: 'Cód.',
                        field: 'IdHardware',
                        width: '8%',
                        primaryKey: true
                    }, {
                        name: 'IMEI',
                        field: 'Identificacao',
                        enableGrouping: false,
                        type: 'number',
                        width: '25%'
                    }, {
                        name: 'Descrição',
                        field: 'DescricaoHardware',
                        enableGrouping: false,
                        width: '50%'
                    }],
                    desiredValue: 'IdHardware',
                    desiredText: 'Identificacao',
                    desiredTextFn: function (data) {
                        if (angular.isDefinedNotNullNotEmpty(data.DescricaoHardware))
                            return data.Identificacao + " (" + data.DescricaoHardware + ")";
                        else return data.Identificacao;
                    },
                    url: 'HardwareAts/ConsultarGrid',
                    paramsMethod: paramsMethod || function () {
                        return null;
                    }
                }
            },

            consultaEstados: function () {
                return {
                    columnDefs: [{
                        name: 'Cód.',
                        field: 'IdEstado',
                        width: '6%',
                        primaryKey: true
                    }, {
                        name: 'Nome',
                        field: 'Nome',
                        width: '40%'
                    }, {
                        name: 'País',
                        field: 'NomePais',
                        serverField: 'Pais.Nome',
                        width: '40%'
                    }],
                    desiredValue: 'IdEstado',
                    desiredText: 'Nome',
                    url: 'EstadoAts/ConsultarGrid'
                };
            },
            consultaFilial: function (paramsMethod) {
                return {
                    columnDefs: [{
                        name: 'Cód.',
                        field: 'IdFilial',
                        width: '6%',
                        primaryKey: true
                    }, {
                        name: 'Nome Fantasia',
                        field: 'NomeFantasia',
                        width: '40%'
                    }, {
                        name: 'CNPJ',
                        field: 'CNPJ',
                        enableGrouping: true,
                        width: '40%'
                    }],
                    desiredValue: 'IdFilial',
                    desiredText: 'NomeFantasia',
                    url: 'FilialAts/ConsultarGrid',
                    paramsMethod: paramsMethod || function () {
                        return null;
                    }
                }
            },
            consultaMotorista: function (paramsMethod) {
                return {
                    columnDefs: [{
                        name: 'Cód.',
                        field: 'IdUsuario',
                        width: '6%',
                        primaryKey: true
                    }, {
                        name: 'Nome',
                        field: 'Nome',
                        width: '40%'
                    }, {
                        name: 'Empresa',
                        serverField: 'Empresa.RazaoSocial',
                        field: 'RazaoSocial',
                        width: '20%'
                    }, {
                        name: 'CPF',
                        field: 'CPF',
                        width: '20%'
                    }],
                    desiredValue: 'IdUsuario',
                    desiredText: 'Nome',
                    url: 'UsuarioApi/ConsultarGrid',
                    paramsMethod: paramsMethod || function () {
                        return null;
                    }
                }
            },
            consultaEvento: function (paramsMethod) {
                return {
                    columnDefs: [{
                        name: 'Cód.',
                        field: 'Id',
                        width: '6%',
                        primaryKey: true
                    }, {
                        name: 'Descricao',
                        field: 'Descricao',
                        width: '40%'
                    }, {
                        name: 'Empresa',
                        field: 'RazaoSocialEmpresa',
                        enableFiltering: false,
                        width: '20%'
                    }, {
                        name: 'Filial',
                        field: 'RazaoSocialFilial',
                        enableFiltering: false,
                        width: '20%'
                    }],
                    removeAtivoFilter: true,
                    desiredValue: 'Id',
                    desiredText: 'Descricao',
                    url: 'QraEventosAts/ConsultarGrid',
                    paramsMethod: paramsMethod || function () {
                        return null;
                    }
                }
            },
            consultaTipoEstabelecimento: function (paramsMethod) {
                return {
                    columnDefs: [{
                        name: 'Código',
                        field: 'IdTipoEstabelecimento',
                        width: '10%',
                        primaryKey: true
                    }, {
                        name: 'Descrição',
                        field: 'Descricao',
                        enableGrouping: false,
                        width: '74%'
                    }],
                    desiredValue: 'IdTipoEstabelecimento',
                    desiredText: 'Descricao',
                    url: 'TipoEstabelecimentoAts/ConsultarGrid',
                    paramsMethod: paramsMethod || function () {
                        return null;
                    }
                };
            },
            consultaMotivoCredenciamento: function (paramsMethod) {
                return {
                    columnDefs: [{
                        name: 'Cód.',
                        width: '7%',
                        primaryKey: true,
                        field: 'IdMotivo'
                    },
                    {
                        name: 'Descrição',
                        field: 'Descricao',
                        width: '48%',
                        enableGrouping: false
                    },
                    {
                        name: 'Empresa',
                        serverField: 'Empresa.RazaoSocial',
                        width: '15%',
                        field: 'RazaoSocialEmpresa'
                    },
                    {
                        name: 'Filial',
                        serverField: 'Filial.RazaoSocial',
                        width: '15%',
                        field: 'RazaoSocialFilial'
                    }
                    ],
                    desiredValue: 'IdMotivo',
                    desiredText: 'Descricao',
                    url: 'MotivoCredenciamentoAts/ConsultarGrid',
                    paramsMethod: paramsMethod || function () {
                        return null;
                    }
                }
            },
            consultaMotivo: function (paramsMethod) {
                return {
                    columnDefs: [{
                        name: 'Cód.',
                        width: '7%',
                        primaryKey: true,
                        field: 'IdMotivo'
                    },
                    {
                        name: 'Descrição',
                        field: 'Descricao',
                        width: '48%',
                        enableGrouping: false
                    },
                    {
                        name: 'Empresa',
                        serverField: 'Empresa.RazaoSocial',
                        width: '15%',
                        field: 'RazaoSocialEmpresa'
                    },
                    {
                        name: 'Filial',
                        serverField: 'Filial.RazaoSocial',
                        width: '15%',
                        field: 'RazaoSocialFilial'
                    }
                    ],
                    desiredValue: 'IdMotivo',
                    desiredText: 'Descricao',
                    url: 'MotivoAts/ConsultarGrid',
                    paramsMethod: paramsMethod || function () {
                        return null;
                    }
                }
            },
            consultaEstabelecimento: function (paramsMethod, customDesiredValue) {
                return {
                    columnDefs: [{
                        name: 'Cód.',
                        width: '7%',
                        primaryKey: true,
                        field: 'IdEstabelecimento'
                    },
                    {
                        name: 'Descrição',
                        field: 'Descricao',
                        width: '68%',
                        enableGrouping: false
                    },
                    {
                        name: 'Credenciado',
                        field: 'Credenciado',
                        width: '10%',
                        enableGrouping: false,
                        type: 'number',
                        enum: true,
                        enumTipo: 'ESimNao'
                    }
                    ],
                    desiredValue: customDesiredValue || 'IdEstabelecimento',
                    desiredText: 'Descricao',
                    url: 'EstabelecimentoAts/ConsultarGrid',
                    paramsMethod: paramsMethod || function () {
                        return null;
                    }
                }
            },
            consultaEstabelecimentoBase: function (paramsMethod) {
                return {
                    columnDefs: [{
                        name: 'Cód.',
                        width: '7%',
                        primaryKey: true,
                        field: 'IdEstabelecimento'
                    },
                    {
                        name: 'Descrição',
                        field: 'Descricao',
                        width: '80%',
                        enableGrouping: false
                    }
                    ],
                    desiredValue: 'IdEstabelecimento',
                    desiredText: 'Descricao',
                    url: 'EstabelecimentoBaseAts/ConsultarGrid',
                    paramsMethod: paramsMethod || function () {
                        return null;
                    }
                }
            },
            consultaAssociacoes: function (paramsMethod) {
                return {
                    columnDefs: [{
                        name: 'Cód.',
                        width: '7%',
                        primaryKey: true,
                        field: 'IdEstabelecimento'
                    },
                    {
                        name: 'Descrição',
                        field: 'Descricao',
                        width: '80%',
                        enableGrouping: false
                    }
                    ],
                    desiredValue: 'IdEstabelecimento',
                    desiredText: 'Descricao',
                    url: 'EstabelecimentoAts/ConsultarAssociacoes',
                    paramsMethod: paramsMethod || function () {
                        return null;
                    }
                }
            },
            // ENUMS
            enums: {
                getEnumDescription: function (enumName, enumValue) {
                    var found = fn.enums[enumName].get().find(function (item) {
                        return item.Value == enumValue
                    });

                    if (angular.isDefinedNotNull(found))
                        return found.Label;
                    else
                        return "";
                },
                EHardwareLeitura: {
                    items: [{
                        Label: "Digital",
                        Value: 0
                    }, {
                        Label: "Analógica",
                        Value: 1
                    }],
                    get: function () {
                        return fn.enums.EHardwareLeitura.items;
                    }
                },
                EHardwareTipo: {
                    items: [{
                        Label: "Entrada",
                        Value: 0
                    }, {
                        Label: "Saída",
                        Value: 1
                    }],
                    get: function () {
                        return fn.enums.EHardwareTipo.items;
                    }
                },
                EStatusProtocoloEvento_SemRejeiado: {
                    items: [{
                        value: 0,
                        label: 'Gerado',
                    }, {
                        value: 1,
                        label: 'Aprovado'
                    },
                    {
                        value: 4,
                        label: 'Liberado'
                    },
                    {
                        label: 'Todos',
                        value: undefined
                    }
                    ],
                    get: function () {
                        return fn.enums.EStatusProtocoloEvento_SemRejeiado.items;
                    }
                },
                EStatusCredenciamento: {
                    items: [{
                        value: 0,
                        label: 'Enviado'
                    }, {
                        value: 1,
                        label: 'Aprovado'
                    },
                    {
                        value: 2,
                        label: 'Rejeitado'
                    }, {
                        value: 3,
                        label: 'Descredenciado'
                    }, {
                        value: 4,
                        label: 'Bloqueado'
                    }, {
                        value: 5,
                        label: 'Cancelado'
                    },
                    {
                        label: 'Todos',
                        value: undefined
                    }
                    ],
                    get: function () {
                        return fn.enums.EStatusCredenciamento.items;
                    }
                },
                EStatusProtocoloEvento: {
                    items: [{
                        value: 0,
                        label: 'Gerado'
                    }, {
                        value: 1,
                        label: 'Aprovado'
                    }, {
                        value: 2,
                        label: 'Rejeitado'
                    }],
                    get: function () {
                        return fn.enums.EStatusProtocoloEvento.items;
                    }
                },
                ESimNao: {
                    items: [{
                        value: 1,
                        label: 'Sim'
                    }, {
                        value: 0,
                        label: 'Não'
                    }],
                    get: function () {
                        return fn.enums.ESimNao.items;
                    }
                },
                EMensagensTratadasNaoTratadas: {
                    items: [{
                        value: 1,
                        label: 'Mensagens tratadas'
                    }, {
                        value: 0,
                        label: 'Mensagens não tratadas'
                    }],
                    get: function () {
                        return fn.enums.EMensagensTratadasNaoTratadas.items;
                    }
                },
                EAprovadoReprovado: {
                    items: [{
                        value: 1,
                        label: 'Aprovado'
                    }, {
                        value: 0,
                        label: 'Reprovado'
                    }],
                    get: function () {
                        return fn.enums.EAprovadoReprovado.items;
                    }
                },
                ECategoriaTipoCarreta: {
                    items: [{
                        value: 1,
                        label: 'Fechada'
                    }, {
                        value: 2,
                        label: 'Aberta'
                    }, {
                        value: 3,
                        label: 'Especial'
                    }],
                    get: function () {
                        return fn.enums.ECategoriaTipoCarreta.items;
                    }
                },
                EStatusLote: {
                    items: [{
                        value: 1,
                        label: 'Carregando'
                    }, {
                        value: 2,
                        label: 'Suspenso'
                    }, {
                        value: 3,
                        label: 'Encerrado'
                    }, {
                        value: 4,
                        label: 'Finalizado'
                    }],
                    get: function () {
                        return fn.enums.ECategoriaTipoCarreta.items;
                    }
                },
                EPedagioLote: {
                    items: [{
                        value: 1,
                        label: 'Incluso'
                    }, {
                        value: 2,
                        label: 'Sem pedágio'
                    }, {
                        value: 3,
                        label: 'Com pedágio'
                    }],
                    get: function () {
                        return fn.enums.ECategoriaTipoCarreta.items;
                    }
                },
                EStatusLoteFila: {
                    items: [{
                        value: 1,
                        label: 'Em trânsito'
                    }, {
                        value: 2,
                        label: 'Confirmado'
                    }, {
                        value: 3,
                        label: 'Na fila'
                    }, {
                        value: 4,
                        label: 'Carregando'
                    }, {
                        value: 5,
                        label: 'Entregue'
                    }, {
                        value: 6,
                        label: 'Desistiu'
                    }, {
                        value: 7,
                        label: 'Embarcado'
                    }, {
                        value: 8,
                        label: 'Removido'
                    }],
                    get: function () {
                        return fn.enums.ECategoriaTipoCarreta.items;
                    }
                },
                EStatusGR: {
                    items: [{
                        value: 1,
                        label: 'Pré cadastro'
                    }, {
                        value: 2,
                        label: 'Em análise'
                    }, {
                        value: 3,
                        label: 'Liberado'
                    }, {
                        value: 4,
                        label: 'Não liberado'
                    }],
                    get: function () {
                        return fn.enums.ECategoriaTipoCarreta.items;
                    }
                },
                EStatusOrdemCarregamento: {
                    items: [{
                        value: 0,
                        label: 'Gerado'
                    }, {
                        value: 1,
                        label: 'Cancelado'
                    }],
                    get: function () {
                        return fn.enums.ECategoriaTipoCarreta.items;
                    }
                },
                EStatusOcorrencia: {
                    items: [{
                        value: 1,
                        label: 'Pendente'
                    }, {
                        value: 2,
                        label: 'Concluída'
                    }, {
                        value: 3,
                        label: 'Revertida'
                    }, {
                        value: 4,
                        label: 'Aguardando Encerramento'
                    }],
                    get: function () {
                        return fn.enums.EStatusOcorrencia.items;
                    }
                },
                EStatusChecklistAgendamento: {
                    items: [{
                        value: 0,
                        label: 'Pendente'
                    }, {
                        value: 1,
                        label: 'Aceito'
                    }, {
                        value: 2,
                        label: 'Rejeitado'
                    }, {
                        value: 3,
                        label: 'Cancelado'
                    }, {
                        value: 4,
                        label: 'Concluído'
                    }],
                    get: function () {
                        return fn.enums.EStatusChecklistAgendamento.items;
                    }
                },
                EStatusCadastros: {
                    items: [{
                        value: 0,
                        label: 'Bloqueado'
                    }, {
                        value: 2,
                        label: 'Pendente de validação'
                    }],
                    get: function () {
                        return fn.enums.EStatusCadastros.items;
                    }
                },
                ETipoPagamentos: {
                    items: [{
                        value: 0,
                        label: 'Adiantamento'
                    },
                    {
                        value: 3,
                        label: 'Avulso'
                    },
                    {
                        value: 2,
                        label: 'Complemento'
                    },
                    {
                        value: 1,
                        label: 'Saldo'
                    },

                    {
                        value: 4,
                        label: 'TarifaANTT'
                    }],
                    get: function () {
                        return fn.enums.ETipoPagamentos.items;
                    }
                },
                EProcessoVinculados: {
                    items: [{
                        value: 0,
                        label: 'Usuário'
                    },
                    {
                        value: 1,
                        label: 'Comprovantes'
                    },
                    {
                        value: 2,
                        label: 'Credenciamento'
                    },
                    {
                        value: 3,
                        label: 'Veículos'
                    }],
                    get: function () {
                        return fn.enums.EProcessoVinculados.items;
                    }
                },
                EFormaPagamentos: {
                    items: [
                        {
                            value: 0,
                            label: 'Depósito'
                        },
                        {
                            value: 1,
                            label: 'Cartão'
                        },
                        {
                            value: 2,
                            label: 'Cheque'
                        },
                        {
                            value: 3,
                            label: 'Outros'
                        },
                        {
                            value: 4,
                            label: 'Pix'
                        },
                    ],
                    get: function () {
                        return fn.enums.EFormaPagamentos.items;
                    }
                },
                EStatusPagamentos: {
                    items: [
                        {
                            value: 0,
                            label: 'Bloqueado'
                        }, {
                            value: 1,
                            label: 'Aberto'
                        }, {
                            value: 2,
                            label: 'Baixado'
                        }, {
                            value: 3,
                            label: 'Cancelado'
                        }, {
                            value: 4,
                            label: 'Processamento'
                        }],
                    get: function () {
                        return fn.enums.EStatusPagamentos.items;
                    }
                },
                EStatusPagamentoEvento: {
                    items: [
                        {
                            value: 0,
                            label: 'Fechado'
                        }, {
                            value: 1,
                            label: 'Aberto'
                        }, {
                            value: 2,
                            label: 'Pendente'
                        }, {
                            value: 3,
                            label: 'Erro'
                        }, {
                            value: 4,
                            label: 'Cancelado'
                        }, {
                            value: 5,
                            label: 'Processando'
                        }, {
                            value: 6,
                            label: 'Não Executado'
                        }],
                    get: function () {
                        return fn.enums.EStatusPagamentoEvento.items;
                    }
                },
                EStatusViagem: {
                    items: [
                        {
                            value: 0,
                            label: 'Bloqueado'
                        },{
                            value: 1,
                            label: 'Aberto'
                        }, {
                            value: 2,
                            label: 'Baixado'
                        }, {
                            value: 3,
                            label: 'Cancelado'
                        }, {
                            value: 4,
                            label: 'Pendente'
                        }],
                    get: function () {
                        return fn.enums.EStatusViagem.items;
                    }
                },
                ETipoEmissaoCIOT: {
                    items: [
                        {
                            value: 0,
                            label: 'Geral'
                        },
                        {
                            value: 1,
                            label: 'Caruana'
                        },
                        {
                            value: 2,
                            label: 'Leasing'
                        }],
                    get: function () {
                        return fn.enums.ETipoEmissaoCIOT.items;
                    }
                },
                ETipoCIOT: {
                    items: [
                        {
                            value: 1,
                            label: 'Padrão'
                        },
                        {
                            value: 3,
                            label: 'Tac Agregado'
                        }],
                    get: function () {
                        return fn.enums.ETipoCIOT.items;
                    }
                },
                ETipoEvento: {
                   items: [
                        {
                            value: 0,
                            label: 'Adiantamento'
                        },
                        {
                            value: 1,
                            label: 'Saldo'
                        },
                        {
                            value: 2,
                            label: 'Complemento'
                        },
                        {
                            value: 3,
                            label: 'Avulso'
                        },
                        {
                            value: 4,
                            label: 'Tarifa ANTT'
                        },
                        {
                            value: 5,
                            label: 'Cancelamento'
                        },
                        {
                            value: 6,
                            label: 'Tarifas'
                        }
                    ],
                    get: function () {
                        return fn.enums.ETipoEvento.items;
                    }
                },
                EFormaPagamentoEvento: {
                   items: [
                        {
                            value: 1,
                            label: 'Depósito'
                        },
                        {
                            value: 4,
                            label: 'Pix'
                        }],
                    get: function () {
                        return fn.enums.EFormaPagamentoEvento.items;
                    }
                },
                EStatusTransacao: {
                    items: [
                        {
                             value: 0,
                             label: 'Fechado'
                        },
                        {
                             value: 1,
                             label: 'Aberto'
                        },
                        {
                             value: 2,
                             label: 'Pendente'
                        },
                        {
                             value: 3,
                             label: 'Erro'
                        },
                        {
                              value: 4,
                              label: 'Cancelado'
                        },
                        {
                              value: 5,
                              label: 'Processando'
                        },
                        {
                              value: 6,
                              label: 'Não Concluído'
                        }],
                     get: function () {
                         return fn.enums.EStatusTransacao.items;
                     }
                 },
                 EStatusCredenciamentoPosto: {
                     items: [
                         {
                              value: 3,
                              label: 'Bloqueado'
                         },
                         {
                              value: 1,
                              label: 'Aprovado'                         
                         },
                         {
                               value: 2,
                               label: 'Reprovado'                         
                         },
                         {
                               value: 0,
                               label: 'Aguardando aprovação'                         
                         }],
                      get: function () {
                          return fn.enums.EStatusCredenciamentoPosto.items;
                      }
                  },
                  EFarolSla: {
                      items: [
                          {
                               value: 3,
                               label: 'Vermelho'
                          },
                          {
                               value: 1,
                               label: 'Verde'                         
                          },
                          {
                                value: 2,
                                label: 'Amarelo'                         
                          }],
                       get: function () {
                           return fn.enums.EFarolSla.items;
                       }
                   },
                 EMetodoAbastecimento: {
                    items: [
                         {
                             value: 2,
                             label: 'Autorização'
                         },
                         {
                             value: 1,
                             label: 'Orçamento'
                        },
                        {
                             value: 3,
                             label: 'Extra'                         
                         }],
                     get: function () {
                         return fn.enums.EMetodoAbastecimento.items;
                     }
                 },
                 EStatusAutorizacaoAbastecimento: {
                    items: [
                         {
                             value: 2,
                             label: 'Cancelada'
                         },
                         {
                             value: 1,
                             label: 'Utilizada'
                        },
                        {
                             value: 0,
                             label: 'Ativa'                         
                         }],
                     get: function () {
                         return fn.enums.EStatusAutorizacaoAbastecimento.items;
                     }
                 },
                 EStatusNotificacoes: {
                    items: [
                         {
                             value: 0,
                             label: 'Fechada'
                         },
                         {
                             value: 1,
                             label: 'Aberta'
                         },
                         {
                             value: 2,
                             label: 'Processando'
                        },
                        {
                             value: 3,
                             label: 'Erro'                         
                         },
                         {
                              value: 4,
                              label: 'Cancelado'                         
                            }],
                     get: function () {
                         return fn.enums.EStatusNotificacoes.items;
                     }
                 },
                ETipoMensagem: {
                    items: [
                        {
                            value: 0,
                            label: 'Texto'
                        },
                        {
                            value: 1,
                            label: 'Imagem'
                        }],
                    get: function () {
                        return fn.enums.ETipoMensagem.items;
                    }
                },
                ECodigoAplicacao: {
                    items: [
                        {
                            value: 0,
                            label: 'Posto'
                        },
                        {
                            value: 1,
                            label: 'CIOT'
                        },
                        {
                            value: 2,
                            label: 'Dock'
                        }],
                    get: function () {
                        return fn.enums.ECodigoAplicacao.items;
                    }
                },
                EStatusCIOT: {
                    items: [
                        {
                            value: 1,
                            label: 'Ciot Gerado'
                        },
                        {
                            value: 2,
                            label: 'Contingência'
                        },
                        {
                            value: 3,
                            label: 'Cancelado'
                        }],
                    get: function () {
                        return fn.enums.EStatusCIOT.items;
                    }
                },
                EStatusTipoOperacao: {
                    items: [
                        {
                            value: 0,
                            label: 'Declarar Operacao de Transporte'
                        },
                        {
                            value: 1,
                            label: 'Cancelar Operacao de Transporte'
                        },
                        {
                            value: 2,
                            label: 'Encerrar Operacao de Transporte'
                        },
                        {
                            value: 3,
                            label: 'Retificar Operacao de Transporte'
                        },
                    ],
                    get: function () {
                        return fn.enums.EStatusTipoOperacao.items;
                    }
                },
                EFormaPagamentoPedagio: {
                    items: [
                        {
                            value: 0,
                            label: 'Vale Pedágio BBC'
                        }],
                    get: function () {
                        return fn.enums.EFormaPagamentoPedagio.items;
                    }
                },
                EStatusPagamentoPedagio: {
                    items: [
                        {
                            value: 0,
                            label: 'Processando'
                        },
                        {
                            value: 1,
                            label: 'Baixado'
                        },
                        {
                            value: 2,
                            label: 'Pendente'
                        },
                        {
                            value: 3,
                            label: 'Pendente cancelamento'
                        },
                        {
                            value: 4,
                            label: 'Cancelado'
                        }],

                    get: function () {
                        return fn.enums.EStatusPagamentoPedagio.items;
                    }
                },
                EStatusPagamentoPedagioConsultaCentralPendencias: {
                    items: [
                        {
                            value: 0,
                            label: 'Processando'
                        },
                        {
                            value: 2,
                            label: 'Pendente'
                        },
                        {
                            value: 3,
                            label: 'Pendente cancelamento'
                        }],
                    get: function () {
                        return fn.enums.EStatusPagamentoPedagioConsultaCentralPendencias.items;
                    }
                },
                ETipoContaDock: {
                    items: [
                        {
                            value: 1,
                            label: 'Corrente'
                        },
                        {
                            value: 2,
                            label: 'Poupança'
                        },
                        {
                            value: 3,
                            label: 'Salario'
                        }],
                    get: function () {
                        return fn.enums.ETipoContaDock.items;
                    }
                },
                EStatusServidorCiot: {
                    items: [
                        {
                            value: 0,
                            label: 'Verde'
                        },
                        {
                            value: 1,
                            label: 'Vermelho'
                        },
                        {
                            value: 2,
                            label: 'Amarelo'
                        }],
                    get: function () {
                        return fn.enums.EStatusServidorCiot.items;
                    }
                },
                EStatusComunicacaoAntt: {
                    items: [
                        {
                            value: 0,
                            label: 'Ok'
                        },
                        {
                            value: 1,
                            label: 'Sem Comunicação'
                        }],
                    get: function () {
                        return fn.enums.EStatusComunicacaoAntt.items;
                    }
                },
                ETipoServidor: {
                    items: [
                        {
                            value: 0,
                            label: 'Servidor velho XML'
                        },
                        {
                            value: 1,
                            label: 'Servidor novo JSON'
                        }],
                    get: function () {
                        return fn.enums.ETipoServidor.items;
                    }
                },
                EContingenciaCiot: {
                    items: [
                        {
                            value: 'S',
                            label: 'Sim'
                        },
                        {
                            value: 'N',
                            label: 'Não'
                        }],
                    get: function () {
                        return fn.enums.EContingenciaCiot.items;
                    }
                },
                ECiotEncerrado: {
                    items: [
                        {
                            value: 'S',
                            label: 'Sim'
                        },
                        {
                            value: 'N',
                            label: 'Não'
                        }],
                    get: function () {
                        return fn.enums.ECiotEncerrado.items;
                    }
                },
                EVersaoIntegracao: {
                    items: [
                        {
                            value: 1,
                            label: 'V1'
                        },
                        {
                            value: 2,
                            label: 'V2'
                        }],
                    get: function () {
                        return fn.enums.EVersaoIntegracao.items;
                    }
                },
                get: function (EnumName) {
                    var access = {
                        headers: {
                            "Content-Type": "application/json; charset=utf-8",
                            "Accept": "application/json"
                        }
                    };

                    var params = {
                        params: {
                            EnumName: EnumName
                        }
                    }
                    return $http.get(URL_SERVER_DEV + "EnumAts" + '/' + "Consultar", params, access).then(function (response) {
                        return response.data;
                    });
                }
            }
        }

        return fn;
    }
})();