(function () {
    'use strict';

    angular.module('bbcWeb.central-pendencias-pagamentos-antecipados.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider
            .state('central-pendencias-pagamentos-antecipados', {
                abstract: true,
                url: "/central-pendencias-pagamentos-antecipados",
                templateUrl: "app/layout/content.html"
            })
            .state('central-pendencias-pagamentos-antecipados.index', {
                url: '/index',
                templateUrl: 'app/entities/central-pendencias-pagamentos-antecipados/central-pendencias-pagamentos-antecipados.html'
            })
            .state('central-pendencias-pagamentos-antecipados.detalhes-pagamento', {
                url: '/detalhes-pagamento/:pagamentoId/:viagemId',
                templateUrl: 'app/entities/central-pendencias-pagamentos-antecipados/detalhes-pagamento.html'
            })
            .state('central-pendencias-pagamentos-antecipados.detalhes-transacao', {
                url: '/detalhes-transacao/:transacaoId/:pagamentoId/:viagemId',
                templateUrl: 'app/entities/pagamentos-antecipados/abas/detalhes-transacao.html'
            });
    }
})();
