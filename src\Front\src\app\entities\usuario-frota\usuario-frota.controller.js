(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('UsuarioFrotaController', UsuarioFrotaController);

    UsuarioFrotaController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR','oitozero.ngSweetAlert', '$uibModal'];

    function UsuarioFrotaController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR, SweetAlert, $uibModal) {
        var vm = this;
        vm.headerItems = [{
            name: 'Cada<PERSON><PERSON>'
        }, {
            name: 'Usuário BBC Frota'
        }];


        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        
        vm.criarNovaSenha = function (id, ativo, mobile) {
            BaseService.post('Portador', "CriarNovaSenha", {
                id: id,
                mobile: mobile
            }).then(function (response) {
                if(response.success){
                    vm.alterarStatus(id, ativo);
                    toastr.success('Usuário BBC frota atualizado com sucesso!')
                }else{
                    toastr.error(response.message)
                }
                vm.gridOptions.dataSource.refresh();
            } );
        }



        vm.criarNovaSenhaUsuarioBloqueado = function (id, status, mobile) {
            BaseService.post('Portador', "CriarNovaSenha", {
                id: id,
                mobile: mobile
            }).then(function (response) {
                if(response.success){
                    vm.bloquearUsuario(id, status);
                }else{
                    toastr.error(response.message)
                }
                vm.gridOptions.dataSource.refresh();
            });
        }


        vm.alterarStatus = function (id, ativo) {
            BaseService.post('UsuarioFrota', "AlterarStatus", {
                id: id
            }).then(function (response) {
                response.success ? (ativo === 1 ? toastr.success('Usuário BBC frota inativado com sucesso!') : toastr.success('Usuário BBC frota reativado com sucesso!')) : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.bloquearUsuario = function (id) {
            BaseService.post('UsuarioFrota', "Bloquear", {
                id: id
            }).then(function (response) {
                toastr.success('Usuário BBC frota atualizado com sucesso!')
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.alterarStatusCancelado = function (id, status) {
            vm.idUsuarioFrota = id;
            vm.insereMotivo();
        };

        vm.insereMotivo = function() {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/usuario-frota/modal/modal-cancelamento.html',
                controller: 'ModalCancelamentoUsuarioFrotaCrudController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'md',
                resolve: {
                    IdUsuarioFrota: function() {
                        return vm.idUsuarioFrota;
                    },

                }
            }).result.then(function() {
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.reativarUsuarioFrota = function (id, ativo) {
            Sistema.Msg.confirm("Deseja realmente reativar o usuário BBC frota?", function () {
                Sistema.Msg.confirm('Deseja gerar um nova senha para o usuário BBC frota ' + id +'?', function () {
                    Sistema.Msg.confirm('Por padrão a senha sera enviada por e-mail, caso desejar enviar por SMS clique em "sim".', function () {
                        vm.criarNovaSenha(id, ativo, true);
                    }, function () {
                        vm.criarNovaSenha(id, ativo, false);
                    });
                }, function () {
                    vm.alterarStatus(id, ativo);
                });
            })
        }

        vm.bloquearUsuarioFrota = function (id, ativo) {
            Sistema.Msg.confirm("Deseja bloquear o usuário BBC frota?",
                function () {
                    vm.bloquearUsuario(id, ativo);
                });
        }


        vm.reativarUsuarioFrotaBloqueado = function (id, status) {
            Sistema.Msg.confirm("Deseja realmente desbloquear o usuário BBC frota?", function () {
                Sistema.Msg.confirm('Deseja gerar um nova senha para o usuário BBC frota ' + id +'?', function () {
                    Sistema.Msg.confirm('Por padrão a senha sera enviada por e-mail, caso desejar enviar por SMS clique em "sim".', function () {
                        vm.criarNovaSenha(id, ativo, true);
                    }, function () {
                        vm.criarNovaSenha(id, ativo, false);
                    });
                }, function () {
                    vm.alterarStatus(id, ativo);
                });
            })
        }



        vm.desbloquearUsuarioFrota = function (id, status) {
            Sistema.Msg.confirm("Deseja realmente desbloquear o usuário BBC frota?", function () {
                Sistema.Msg.confirm('Deseja gerar um nova senha para o usuário BBC frota ' + id +'?', function () {
                    Sistema.Msg.confirm('Por padrão a senha sera enviada por e-mail, caso desejar enviar por SMS clique em "sim".', function () {
                        vm.criarNovaSenhaUsuarioBloqueado(id, status, true);
                    }, function () {
                        vm.criarNovaSenhaUsuarioBloqueado(id, status, false);
                    });
                }, function () {
                    vm.bloquearUsuario(id, status);
                });
            });
        }

        vm.cancelarUsuarioFrota = function (id, status) {
            Sistema.Msg.confirm("Deseja cancelar o usuário BBC frota?",
                function () {
                    vm.alterarStatusCancelado(id, "Cancelado");
                });
        }

        function abrirDialogoAdmin() {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/usuario-frota/modal/modal-boas-vindas-admin.html', // ajusta esse caminho pro seu HTML
                controller: function ($uibModalInstance) {
                    var vm = this;
                    vm.fechar = function () {
                        $uibModalInstance.close();
                    };
                },
                controllerAs: 'vm',
                size: 'md'
            });
        }

        vm.gridOptions = {
            data: [],
            onRegisterApi: function (gridApi) {
                BaseService.dataGrid.defaultOnRegisterApi(gridApi);
                vm.gridApi = gridApi;

                var coluna = vm.gridOptions.columnDefs.find(c => c.name === 'Empresa');
                if (coluna) {
                    coluna.visible = showCo$rootScope.usuarioLogado.administrador == true;
                    vm.gridApi.core.notifyDataChange(uiGridConstants.dataChange.COLUMN);
                }
            },
            parametrosExtras: {
                IdEmpresa: $rootScope.usuarioLogado.idEmpresa
            },
            dataSource: {
                url: "UsuarioFrota/ConsultarGrid"
            },
            columnDefs: [{
                name: 'Ações',
                width: 120,
                cellTemplate:  '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                    <button ng-disabled="row.entity.ativo===0 && row.entity.status!==\'Cancelado\'"   tooltip-placement="right" uib-tooltip="Editar / Visualizar" type="button" ui-sref="usuario-frota.usuario-frota-crud({link: row.entity.id})"\
                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                        <i ng-class="row.entity.status!==\'Cancelado\' ? \'fa fa-edit\' : \'fa fa-solid fa-eye\'"></i>\
                    </button>\
                    <button type="button" ng-disabled="row.entity.status=== \'Cancelado\'" tooltip-placement="right"\
                        uib-tooltip="Ativar / Inativar"\
                        ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)" \
                        ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\<i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times-circle\'"></i>\
                    </button>\
                    <button type="button" ng-disabled="row.entity.status=== \'Cancelado\'" tooltip-placement="right"\
                        uib-tooltip="Bloquear / Desbloquear"\
                        ng-click="row.entity.status === \'Bloqueado\' ? grid.appScope.vm.desbloquearUsuarioFrota(row.entity.id, row.entity.ativo)\
                         : grid.appScope.vm.bloquearUsuarioFrota(row.entity.id, row.entity.status)" \
                         ng-style="{\'background-color\': row.entity.status === \'Bloqueado\' ? \' #d9534f\' : \' #f8ac59\', \'color\': \' #ffffff\'}"\
                         ng-class=" \'btn btn-xs\'">\<i ng-class="\'fa fa-ban\'"></i>\
                    </button>\
                     <button type="button" ng-disabled="row.entity.status=== \'Cancelado\'" tooltip-placement="right"\
                        uib-tooltip="Cancelar / Cancelado"\
                        ng-click="grid.appScope.vm.cancelarUsuarioFrota(row.entity.id, row.entity.status)" \
                         ng-style="{\'background-color\': row.entity.status === \'Cancelado\' ? \' #e58f8a\' : \' #cc241b\', \'color\': \' #ffffff\'}"\
                         ng-class=" \'btn btn-xs\'">\<i ng-class="\'fa fa-power-off\'"></i>\
                    </button>\
                </div>'
            }, {
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type : 'number',
                enableFiltering: true
            }, {
                name: 'Nome',
                width: '*',
                minWidth: 160,
                field: 'nome',
                serverField: 'nome',
                enableFiltering: true
            }, 
            {
                name: 'CPF',
                displayName: 'CPF',
                width: 150,
                field: 'cpfCnpj',
                serverField: 'cpfCnpj',
                enableFiltering: true
            },
            {
                name: 'CNH',
                displayName: 'CNH',
                width: 150,
                field: 'cnh',
                serverField: 'CNH',
                enableFiltering: true
            },
            {
                name: 'Placa',
                displayName: 'Placa',
                width: 150,
                field: 'placa',
                serverField: 'placa',
                enableFiltering: true
            },
            {
                name: 'Celular',
                displayName: 'Celular',
                width: 120,
                field: 'celular',
                serverField: 'celular',
                enableFiltering: true
            },

            {
                name: 'Email',
                displayName: 'Email',
                width: '*',
                minWidth: 220,
                field: 'email',
                serverField: 'email',
                enableFiltering: true
            },
            {
                name: 'Status',
                displayName: 'Status',
                width: '*',
                minWidth: 160,
                enum: true,
                enumTipo: 'EStatusPortador',
                type: 'text',
                field: 'status',
                enableFiltering: true
            },
            {
                name: 'Data Último Acesso',
                displayName: 'Data Último Acesso',
                width: 150,
                field: 'dataUltimoAcesso',
                serverField: 'dataUltimoAcesso',
                enableFiltering: false
            },
            {
                name: 'Usuario Cadastro',
                displayName: 'Usuario Cadastro',
                width: 150,
                field: 'usuarioCadastro',
                serverField: 'UsuarioCadastro.Login',
                enableFiltering: false
            },
            {
                name: 'Data Cadastro',
                displayName: 'Data Cadastro',
                width: 150,
                field: 'dataCadastro',
                serverField: 'dataCadastro',
                enableFiltering: false
            },
            {
                name: 'Usuario Bloqueio',
                displayName: 'Usuario Bloqueio',
                width: 150,
                field: 'usuarioBloqueio',
                serverField: 'UsuarioBloqueio.Login',
                enableFiltering: true
            },
            {
                name: 'Data Bloqueio',
                displayName: 'Data Bloqueio',
                width: 150,
                field: 'dataBloqueio',
                serverField: 'dataBloqueio',
                enableFiltering: false
            },
            {
                name: 'Data Bloqueio Mobile',
                displayName: 'Data Bloqueio Mobile',
                width: 150,
                field: 'dataBloqueioMobile',
                serverField: 'dataBloqueioMobile',
                enableFiltering: false
            },

            {
                name: 'Usuario Desbloqueio',
                displayName: 'Usuario Desbloqueio',
                width: 150,
                field: 'usuarioDesbloqueio',
                serverField: 'UsuarioDesbloqueio.Login',
                enableFiltering: true
            },
            {
                name: 'Data Desbloqueio',
                displayName: 'Data Desbloqueio',
                width: 150,
                field: 'dataDesbloqueio',
                serverField: 'dataDesbloqueio',
                enableFiltering: false
            },
            {
                name: 'Data Desbloqueio Mobile',
                displayName: 'Data Desbloqueio Mobile',
                width: 150,
                field: 'dataDesbloqueioMobile',
                serverField: 'dataDesbloqueioMobile',
                enableFiltering: false
            },
            {
                name: 'Quantidade Erro Senha',
                displayName: 'Quantidade Erro Senha',
                width: 150,
                field: 'quantidadeErroSenhaVsParametro',
                serverField: 'quantidadeErroSenhaVsParametro',
                enableFiltering: false
            },
            {
                name: 'Motivo Cancelamento',
                displayName: 'Motivo Cancelamento',
                width: 250,
                field: 'motivoCancelamento',
                serverField: 'motivoCancelamento',
                enableFiltering: true
            },
            {
                name: 'Usuario Cancelamento',
                displayName: 'Usuario Cancelamento',
                width: 150,
                field: 'usuarioCancelamento',
                serverField: 'UsuarioCancelamento.Login',
                enableFiltering: true
            },
            {
                name: 'Data Cancelamento',
                displayName: 'Data Cancelamento',
                width: 150,
                field: 'dataCancelamento',
                serverField: 'dataCancelamento',
                enableFiltering: false
            },
            {
                name: 'Empresa',
                displayName: 'Empresa',
                width: 200,
                field: 'empresa',
                serverField: 'EmpresaFrota.NomeFantasia',
                enableFiltering: true,
                visible: vm.isAdmin()
            },
            {
                name: 'Senha Provisória',
                displayName: 'Senha Provisória',
                width: 200,
                field: 'senhaProvisoria',
                serverField: 'senhaProvisoria',
                enum: true,
                enumTipo: 'ESimNao',
                enableFiltering: true,
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.senhaProvisoria === 1"> Sim </p>\
                                        <p ng-show="row.entity.senhaProvisoria === 0"> Não </p>\
                                   </div>'
            }],
        };



        vm.showCompanies = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

         // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('UsuarioFrotaController', vm, "Usuários BBC Frota", "UsuarioFrotaCrudController", "usuario-frota.index");
        });

        var selfScope = PersistentDataService.get('UsuarioFrotaController');
        var filho = PersistentDataService.get('UsuarioFrotaCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('usuario-frota.usuario-frota-crud', {
                    link: filho.data.portador.id > 0 ? filho.data.portador.id : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();
