<div id="PagamentosAntecipadosController" ng-controller="PagamentosAntecipadosController as vm">
    <form-header items="vm.headerItems" head="'Pagamentos Antecipados'"></form-header>
    
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Pagamentos Antecipados</h5>
                        <div ibox-tools></div>
                    </div>
                    
                    <div class="ibox-content">
                        <!-- Filtros -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Período</label>
                                    <input type="text" 
                                           class="form-control" 
                                           ng-model="vm.filtros.periodo" 
                                           date-range-picker 
                                           options="vm.dateRangeOptions"
                                           placeholder="Selecione o período">
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Empresa</label>
                                    <select class="form-control" 
                                            ng-model="vm.filtros.empresaId"
                                            ng-options="empresa.id as empresa.razaoSocial for empresa in vm.empresas">
                                        <option value="">Todas as empresas</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>CPF/CNPJ Proprietário</label>
                                    <input type="text" 
                                           class="form-control" 
                                           ng-model="vm.filtros.cpfCnpjProprietario"
                                           placeholder="Digite o CPF/CNPJ">
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="button" 
                                                class="btn btn-primary" 
                                                ng-click="vm.consultar()">
                                            <i class="fa fa-search"></i> Consultar
                                        </button>
                                        <button type="button" 
                                                class="btn btn-default" 
                                                ng-click="vm.limparFiltros()">
                                            <i class="fa fa-eraser"></i> Limpar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Total e Ações -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <strong>Total: </strong>
                                    <span class="text-primary">{{vm.totalPagamentos | currency:"R$ "}}</span>
                                </div>
                            </div>
                            
                            <div class="col-md-6 text-right">
                                <button tooltip-placement="top" 
                                        ng-click="vm.gridOptions.dataSource.refresh();" 
                                        uib-tooltip="Atualizar" 
                                        type='button' 
                                        class="btn btn-labeled btn-default">
                                    <i class="fa fa-refresh"></i>
                                    <span class="pl-5">Atualizar</span>
                                </button>
                                
                                <button tooltip-placement="top" 
                                        ng-click="vm.exportarRelatorio()" 
                                        ng-disabled="vm.desabilitarBtnRelatorio"
                                        uib-tooltip="Exportar Relatório" 
                                        type='button' 
                                        class="btn btn-labeled btn-success">
                                    <i class="fa fa-file-excel-o"></i>
                                    <span class="pl-5">Exportar Relatório</span>
                                </button>
                            </div>
                        </div>

                        <hr>

                        <!-- Grid -->
                        <div ui-grid="vm.gridOptions" 
                             ng-style="{height: vm.gridOptions.getGridHeight()}" 
                             class="grid" 
                             style="width: 100%;" 
                             ui-grid-pinning 
                             ui-grid-save-state
                             ui-grid-pagination 
                             ui-grid-auto-resize 
                             ui-grid-resize-columns 
                             ui-grid-grouping>
                        </div>

                        <!-- Tabela para exportação (oculta) -->
                        <div style="display: none;">
                            <table id="exportable-xls">
                                <thead>
                                    <tr>
                                        <th>Código Pagamento</th>
                                        <th>Código Viagem</th>
                                        <th>PagamentoExternoId</th>
                                        <th>ViagemExternoId</th>
                                        <th>Status</th>
                                        <th>Status Antecipação</th>
                                        <th>Valor</th>
                                        <th>Descrição</th>
                                        <th>CPF/CNPJ Proprietário</th>
                                        <th>Nome Proprietário</th>
                                        <th>Razão Social Empresa</th>
                                        <th>CNPJ Empresa</th>
                                        <th>Data Previsão Pagamento</th>
                                        <th>Data Baixa Parcela</th>
                                        <th>Data Cadastro Antecipação</th>
                                        <th>Data Alteração Antecipação</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="item in vm.dadosRelatorio">
                                        <td>{{item.id}}</td>
                                        <td>{{item.viagemId}}</td>
                                        <td>{{item.pagamentoExternoId}}</td>
                                        <td>{{item.viagemExternoId}}</td>
                                        <td>{{item.statusDescricao}}</td>
                                        <td>{{item.statusAntecipacaoDescricao}}</td>
                                        <td>{{item.valor | currency:"R$ "}}</td>
                                        <td>{{item.antecipacaoMotivo}}</td>
                                        <td>{{item.cpfCnpjProprietario}}</td>
                                        <td>{{item.nomeProprietario}}</td>
                                        <td>{{item.razaoSocialEmpresa}}</td>
                                        <td>{{item.cnpjEmpresa}}</td>
                                        <td>{{item.dataPrevisaoPagamento}}</td>
                                        <td>{{item.dataBaixa}}</td>
                                        <td>{{item.dataCadastroAntecipacao}}</td>
                                        <td>{{item.dataAlteracaoAntecipacao}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
