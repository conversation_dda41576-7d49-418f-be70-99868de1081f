<div ng-controller="PagamentosAntecipadosController as vm">
    <form-header items="vm.headerItems" head="'Painel de pagamentos antecipados'"
        state="pagamentos-antecipados">
    </form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins" style="border-radius: 8px;">
                    <div class="ibox-title-2x"
                        style="display: flex; align-items: center; justify-content: space-between; min-height: 50px; padding: 20px 20px 20px 20px;">
                        <div style="display: flex; align-items: center;">
                            <h5 style="margin: 0; display: flex; align-items: center;">
                                <span class="hidden-xs">Painel de pagamentos antecipados</span>
                                <span class="visible-xs">Pagamentos</span>
                            </h5>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <button type="button" class="btn btn-primary btn-sm" ng-click="vm.consultar()"
                                style="border-radius: 6px; font-weight: 500;">
                                <i class="fa fa-search"></i>
                                <span class="hidden-xs ml-5">Consultar</span>
                            </button>
                            <button type="button" class="btn btn-default btn-sm" ng-click="vm.limparTotais()"
                                style="border-radius: 6px; font-weight: 500;">
                                <i class="fa fa-eraser"></i>
                                <span class="hidden-xs ml-5">Limpar</span>
                            </button>
                            <button type="button" class="btn btn-success btn-sm" ng-click="vm.gridOptions.dataSource.refresh()"
                                style="border-radius: 6px; font-weight: 500;">
                                <i class="fa fa-refresh"></i>
                                <span class="hidden-xs ml-5">Atualizar</span>
                            </button>
                           
                        </div>
                    </div>

        
                    <div class="ibox-content">
                        <div class="alert alert-info alert-dismissible" ng-show="!vm.alertaFechado"
                            style="border-radius: 6px;">
                            <button type="button" class="close" ng-click="vm.fecharAlerta()" aria-label="Fechar">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <div style="display: flex; align-items: flex-start;">
                                <i class="fa fa-info-circle"
                                    style="font-size: 20px; margin-right: 10px; margin-top: 2px; color: #5bc0de;"></i>
                                <div>
                                    <strong>Informação!</strong> Esta tela apresenta todos os pagamentos antecipados do sistema:
                                    <ul style="margin: 8px 0 0 0; padding-left: 20px;">
                                        <li><strong>Status Aberto</strong>: Pagamentos ainda não processados</li>
                                        <li><strong>Status Fechado</strong>: Pagamentos já processados e finalizados</li>
                                        <li><strong>Antecipação</strong>: Status do processo de antecipação (Aguardando, Aprovado, Erro)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Filtros Expansíveis -->
                        <div class="panel panel-default" style="border-radius: 8px; border: 1px solid #e9ecef; margin-bottom: 20px;">
                            <div class="panel-heading" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; cursor: pointer;" 
                                ng-click="vm.toggleFiltros()">
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <h6 style="margin: 0; font-weight: 600; color: #495057;">
                                        <i class="fa fa-filter" style="margin-right: 8px; color: #6c757d;"></i>
                                        Filtros de Pesquisa
                                    </h6>
                                    <i class="fa" ng-class="vm.filtrosExpandidos ? 'fa-chevron-up' : 'fa-chevron-down'" 
                                        style="color: #6c757d; transition: transform 0.2s;"></i>
                                </div>
                            </div>
                            <div class="panel-body" ng-show="vm.filtrosExpandidos" style="padding: 20px;">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label">Período:</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" 
                                                    date-range-picker 
                                                    ng-model="vm.filtros.periodo" 
                                                    options="vm.dateOptions"
                                                    style="border-radius: 4px;">
                                                <span class="input-group-addon" style="border-radius: 0 4px 4px 0;">
                                                    <i class="fa fa-calendar"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                     <div class="col-md-4" ng-show="vm.usuAdm">
                                        <div class="row">
                                            <label class="text-left">Empresa</label>
                                            <div class="form-group" ng-show="vm.usuAdm">
                                                
                                                <consulta-padrao-modal tabledefinition="vm.consultaEmpresa"
                                                    idname="consultaEmpresa" placeholder="'Selecione uma Empresa'"
                                                    required-message="'Empresa é obrigatória'"
                                                    ng-required="vm.isAdmin()" directivesizes="'col-xs-12'"
                                                    labelsize="'sr-only'">
                                                </consulta-padrao-modal>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label">CPF/CNPJ Proprietário:</label>
                                            <input type="text" class="form-control" 
                                                ng-model="vm.filtros.cpfCnpjProprietario" 
                                                placeholder="Digite o CPF/CNPJ do proprietário"
                                                style="border-radius: 4px;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Cards de Totais -->
                        <div class="row" style="margin-top: 15px;">
                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="alert alert-info" style="margin-bottom: 10px; padding: 10px;">
                                    <div style="text-align: center;">
                                        <i class="fa fa-calendar-o"></i>
                                        <div><strong class="hidden-xs">Período Selecionado</strong></div>
                                        <div><strong class="visible-xs-block">Período</strong></div>
                                        <div class="text-primary" style="font-size: 16px; font-weight: bold;">
                                            {{vm.totalPeriodoSelecionado | currency:"R$ "}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="alert alert-warning" style="margin-bottom: 10px; padding: 10px;">
                                    <div style="text-align: center;">
                                        <i class="fa fa-calendar-minus"></i>
                                        <div><strong class="hidden-xs">Esta Semana</strong></div>
                                        <div><strong class="visible-xs-block">Semana</strong></div>
                                        <div class="text-warning" style="font-size: 16px; font-weight: bold;">
                                            {{vm.totalSemana | currency:"R$ "}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="alert alert-success" style="margin-bottom: 10px; padding: 10px;">
                                    <div style="text-align: center;">
                                        <i class="fa fa-calendar"></i>
                                        <div><strong class="hidden-xs">Este Mês</strong></div>
                                        <div><strong class="visible-xs-block">Mês</strong></div>
                                        <div class="text-success" style="font-size: 16px; font-weight: bold;">
                                            {{vm.totalMes | currency:"R$ "}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="alert alert-danger" style="margin-bottom: 10px; padding: 10px;">
                                    <div style="text-align: center;">
                                        <i class="fa fa-calculator"></i>
                                        <div><strong class="hidden-xs">Total Ano</strong></div>
                                        <div><strong class="visible-xs-block">Total</strong></div>
                                        <div class="text-danger" style="font-size: 16px; font-weight: bold;">
                                            {{vm.totalPagamentosAno | currency:"R$ "}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 12px 0;"></div>

                        <!-- Grid de Dados - Só aparece quando há dados -->
                        <div class="row" ng-if="!vm.semDados">
                            <div class="col-md-12">
                                <div ui-grid="vm.gridOptions"
                                     ui-grid-resize-columns
                                     ui-grid-move-columns
                                     ui-grid-pagination
                                     ui-grid-selection
                                     ui-grid-exporter
                                     class="grid"
                                     style="height: 500px; border-radius: 8px;">
                                </div>
                            </div>
                        </div>

                        <!-- Mensagem quando não há pagamentos -->
                        <div class="row" ng-if="vm.semDados">
                            <div class="col-md-12">
                                <div style="text-align: center; padding: 80px 20px; color: #6c757d; background: white; border-radius: 8px; border: 1px solid #e9ecef;">
                                    <i class="fa fa-search" style="font-size: 64px; color: #dee2e6; margin-bottom: 25px;"></i>
                                    <h3 style="color: #6c757d; font-weight: 500; margin-bottom: 15px;">Nenhum pagamento encontrado</h3>
                                    <p style="color: #adb5bd; margin: 0; font-size: 16px;">
                                        Não há pagamentos antecipados para o período e filtros selecionados.<br>
                                        Tente ajustar os filtros ou selecionar um período diferente.
                                    </p>
                                    <div style="margin-top: 25px;">
                                        <button type="button" class="btn btn-primary" ng-click="vm.limparFiltros()" style="margin-right: 10px;">
                                            <i class="fa fa-eraser"></i> Limpar Filtros
                                        </button>
                                        <button type="button" class="btn btn-default" ng-click="vm.consultar()">
                                            <i class="fa fa-refresh"></i> Atualizar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>


                </div>
            </div>
        </div>
    </div>
</div>
