<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <!-- Inform<PERSON><PERSON><PERSON><PERSON> da Viagem -->
        <div class="row" style="margin-bottom: 40px;">
            <div class="col-xs-12 col-sm-12 col-md-8 col-lg-6">
                <div class="panel panel-default" style="border: none; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 8px;">
                    <div class="panel-body" style="padding: 20px; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);">
                        <div class="row">
                            <div class="col-xs-12">
                                <label style="font-size: 13px; color: #6c757d; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 8px; display: block;">
                                    <PERSON><PERSON><PERSON> da Viagem
                                </label>
                                <div style="position: relative;">
                                    <input type="text" ng-disabled="true" ng-model="vm.codViagem"
                                           class="form-control"
                                           style="font-size: 24px; font-weight: 700; color: #056233;
                                                  background-color: transparent; border: none; border-bottom: 3px solid #056233;
                                                  border-radius: 0; padding: 8px 0; box-shadow: none; font-family: 'Courier New', monospace;
                                                  text-align: center; letter-spacing: 2px;" />
                                    <div style="position: absolute; top: -5px; right: 0; color: #056233; font-size: 18px;">
                                        <i class="fa fa-hashtag"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sub-abas menores para Valores e Veículos -->
        <div class="row" style="margin-bottom: 20px;">
            <div class="col-lg-12">
                <div form-wizard steps="2">
                    <div class="form-wizard">
                        <ol class="row" style="margin-bottom: 10px;">
                            <li ng-click="wizard.go(1)" class="control-label col-xs-12 col-md-6"
                                style="text-align: center;" ng-class="{'active': wizard.active(1)}">
                                <h4>Valores</h4>
                            </li>
                            <li ng-click="wizard.go(2);" class="control-label col-xs-12 col-md-6"
                                style="text-align: center;" ng-class="{'active': wizard.active(2)}">
                                <h4>Veículos</h4>
                            </li>
                        </ol>
                        <div id="activateTab1" ng-show="wizard.active(1)">
                            <div ng-include="'app/entities/viagens/pagamentos/abas/abas-detalhes-viagem/aba-valores.html'"
                                class="form-horizontal"> </div>
                        </div>
                        <div id="activateTab2" ng-show="wizard.active(2)">
                            <div ng-include="'app/entities/viagens/pagamentos/abas/abas-detalhes-viagem/aba-veiculos.html'"
                                class="form-horizontal"> </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Seção de Pagamentos -->
        <div>
            <hr-label dark="true" title="'Pagamentos'"></hr-label>
            <br><br>
            <div class="row">
                <div class="col-lg-12">
                    <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid"
                        style="width: 100%;" ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize
                        ui-grid-resize-columns ui-grid-grouping>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>