<style>
    .viagem-info-section {
        margin-bottom: 25px;
    }

    .viagem-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-left: 4px solid #056233;
        border-radius: 6px;
        padding: 16px 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .viagem-card-content {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .viagem-icon {
        background-color: #056233;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
    }

    .viagem-label {
        font-size: 12px;
        color: #6c757d;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 2px;
    }

    .viagem-codigo {
        font-size: 18px;
        font-weight: 700;
        color: #056233;
        letter-spacing: 1px;
    }

    .sub-tabs-section {
        margin-bottom: 20px;
    }

    .sub-tabs-list {
        margin-bottom: 10px;
    }

    .pagamentos-section {
        margin-bottom: 30px;
    }

    .pagamentos-grid {
        width: 100%;
    }

    /* Estilos para a seção de detalhes da viagem */
    .detalhes-viagem .form-group {
        margin-bottom: 20px;
    }

    .detalhes-viagem .field-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
        font-size: 13px;
    }

    .detalhes-viagem .field-input {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        font-size: 14px;
    }

    .detalhes-viagem .json-container {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .detalhes-viagem .json-label {
        font-weight: 600;
        color: #056233;
        margin-bottom: 10px;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .detalhes-viagem .json-textarea {
        background-color: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        resize: vertical;
        min-height: 200px;
        transition: border-color 0.3s ease;
    }

    .detalhes-viagem .json-textarea:hover {
        border-color: #056233;
        cursor: pointer;
    }

    .detalhes-viagem .info-section {
        background-color: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .detalhes-viagem .section-title {
        font-size: 16px;
        font-weight: 700;
        color: #056233;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }

    .detalhes-viagem .cancelamento-section {
        border-left: 4px solid #dc3545;
        background-color: #fff5f5;
    }
</style>

<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row viagem-info-section">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-5">
                <div class="viagem-card">
                    <div class="viagem-card-content">
                        <div class="viagem-icon">
                            <i class="fa fa-hashtag"></i>
                        </div>
                        <div>
                            <div class="viagem-label">
                                Código da Viagem
                            </div>
                            <div class="viagem-codigo">
                                <span ng-bind="vm.codViagem"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="detalhes-viagem">
            <div class="info-section">
                <div class="section-title">
                    <i class="fa fa-code"></i> Dados da Viagem
                </div>

                <div>
                    <div class="col-md-6" style="padding-right: 15px;">
                        <div class="form-group">
                            <label class="field-label">Data/Hora Requisição</label>
                            <input type="text" ng-disabled="true" ng-model="vm.logsViagem.dataEnvio"
                                class="form-control field-input" />
                        </div>
                    </div>
                    <div class="col-md-6" style="padding-left: 15px;">
                        <div class="form-group">
                            <label class="field-label">Data/Hora Resposta</label>
                            <input type="text" ng-disabled="true" ng-model="vm.logsViagem.dataRetorno"
                                class="form-control field-input" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="json-container">
                            <div class="json-label">
                                <i class="fa fa-upload"></i>
                                JSON Envio
                                <small ng-if="vm.logsViagem.jsonEnvio" class="text-muted">(Clique para copiar)</small>
                            </div>
                            <a
                                ng-click="vm.logsViagem.jsonEnvio ? vm.jsonUtils.copyToClipboard(vm.logsViagem.jsonEnvio, 'JSON de envio copiado') : null">
                                <textarea ng-disabled="!vm.logsViagem.jsonEnvio"
                                    readonly name="jsonEnvio" class="form-control json-textarea"
                                    placeholder="Nenhum JSON de envio disponível">{{ vm.logsViagem.jsonEnvio ? (vm.jsonUtils.prettyPrint(vm.logsViagem.jsonEnvio) || '') : '' }}</textarea>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="json-container">
                            <div class="json-label">
                                <i class="fa fa-download"></i>
                                JSON Resposta
                                <small ng-if="vm.logsViagem.jsonRetorno" class="text-muted">(Clique para copiar)</small>
                            </div>
                            <a ng-click="vm.logsViagem.jsonRetorno ? vm.jsonUtils.copyToClipboard(vm.logsViagem.jsonRetorno, 'JSON de resposta copiado') : null">
                                <textarea ng-disabled="!vm.logsViagem.jsonRetorno"
                                    readonly name="jsonRetorno" class="form-control json-textarea"
                                    placeholder="Nenhum JSON de resposta disponível">{{ vm.logsViagem.jsonRetorno ? (vm.jsonUtils.prettyPrint(vm.logsViagem.jsonRetorno) || '') : '' }}</textarea>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div ng-if="vm.logsViagem.dataCadastroCancelamento || vm.logsViagem.dataRetornoCancelamento || vm.logsViagem.jsonEnvioCancelamento || vm.logsViagem.jsonRetornoCancelamento"
                class="info-section cancelamento-section">
                <div class="section-title">
                    <i class="fa fa-ban"></i> Dados do Cancelamento
                </div>

               
                <div ng-if="vm.logsViagem.dataCadastroCancelamento || vm.logsViagem.dataRetornoCancelamento" style="padding-right: 15px; padding-left: 15px;">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="field-label">Data/Hora Requisição Cancelamento</label>
                            <input type="text" ng-disabled="true" ng-model="vm.logsViagem.dataCadastroCancelamento"
                                class="form-control field-input" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="field-label">Data/Hora Resposta Cancelamento</label>
                            <input type="text" ng-disabled="true" ng-model="vm.logsViagem.dataRetornoCancelamento"
                                class="form-control field-input" />
                        </div>
                    </div>
                </div>

                <div ng-if="vm.logsViagem.jsonEnvioCancelamento || vm.logsViagem.jsonRetornoCancelamento" class="row">
                    <div class="col-md-6">
                        <div class="json-container">
                            <div class="json-label">
                                <i class="fa fa-upload"></i>
                                JSON Envio Cancelamento
                                <small ng-if="vm.logsViagem.jsonEnvioCancelamento" class="text-muted">(Clique para
                                    copiar)</small>
                            </div>
                            <a
                                ng-click="vm.logsViagem.jsonEnvioCancelamento ? vm.jsonUtils.copyToClipboard(vm.logsViagem.jsonEnvioCancelamento, 'JSON de envio do cancelamento copiado') : null">
                                <textarea ng-disabled="!vm.logsViagem.jsonEnvioCancelamento"
                                    readonly name="jsonEnvioCancelamento" class="form-control json-textarea"
                                    placeholder="Nenhum JSON de envio de cancelamento disponível">{{ vm.logsViagem.jsonEnvioCancelamento ? (vm.jsonUtils.prettyPrint(vm.logsViagem.jsonEnvioCancelamento) || '') : '' }}</textarea>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="json-container">
                            <div class="json-label">
                                <i class="fa fa-download"></i>
                                JSON Resposta Cancelamento
                                <small ng-if="vm.logsViagem.jsonRetornoCancelamento" class="text-muted">(Clique para
                                    copiar)</small>
                            </div>
                            <a
                                ng-click="vm.logsViagem.jsonRetornoCancelamento ? vm.jsonUtils.copyToClipboard(vm.logsViagem.jsonRetornoCancelamento, 'JSON de resposta do cancelamento copiado') : null">
                                <textarea ng-disabled="!vm.logsViagem.jsonRetornoCancelamento" 
                                    readonly name="jsonRetornoCancelamento" class="form-control json-textarea"
                                    placeholder="Nenhum JSON de resposta de cancelamento disponível">{{ vm.logsViagem.jsonRetornoCancelamento ? (vm.jsonUtils.prettyPrint(vm.logsViagem.jsonRetornoCancelamento) || '') : '' }}</textarea>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Termina AQUI -->

        <div class="row sub-tabs-section">
            <div class="col-lg-12">
                <div form-wizard steps="2">
                    <div class="form-wizard">
                        <ol class="row sub-tabs-list">
                            <li ng-click="wizard.go(1)" class="col-xs-12 col-md-6"
                                ng-class="{'active': wizard.active(1)}">
                                <h4>Valores</h4>
                            </li>
                            <li ng-click="wizard.go(2)" class="col-xs-12 col-md-6"
                                ng-class="{'active': wizard.active(2)}">
                                <h4>Veículos</h4>
                            </li>
                        </ol>

                        <div id="activateTab1" ng-show="wizard.active(1)">
                            <div ng-include="'app/entities/viagens/pagamentos/abas/abas-detalhes-viagem/aba-valores.html'"
                                class="form-horizontal"></div>
                        </div>

                        <div id="activateTab2" ng-show="wizard.active(2)">
                            <div ng-include="'app/entities/viagens/pagamentos/abas/abas-detalhes-viagem/aba-veiculos.html'"
                                class="form-horizontal"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="pagamentos-section">
            <hr-label dark="true" title="'Pagamentos'"></hr-label>
            <br><br>
            <div class="row">
                <div class="col-lg-12">
                    <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}"
                        class="grid pagamentos-grid" ui-grid-pinning ui-grid-save-state ui-grid-pagination
                        ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>