<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div form-wizard steps="2">
                <div class="form-wizard">
                    <ol class="row">
                        <li ng-click="wizard.go(1)" class="control-label col-xs-12 col-sm-12 col-md-6 col-lg-6"
                            style="text-align: center;" ng-class="{'active': wizard.active(1)}">
                            <h4>Valores</h4>
                        </li>
                        <li ng-click="wizard.go(2); vm.gridOptions.dataSource.refresh();"
                            class="control-label col-xs-12 col-sm-12 col-md-6 col-lg-6" style="text-align: center;"
                            ng-class="{'active': wizard.active(2)}">
                            <h4>Veículos</h4>
                        </li>
                    </ol>
                    <div id="activateTab1" ng-show="wizard.active(1)">
                        <div ng-include="'app/entities/cadastro-posto/cadastro/abas/abas-abastecimento/aba-combustiveis.html'"
                            class="form-horizontal"> </div>
                    </div>
                    <div id="activateTab2" ng-show="wizard.active(2)">
                        <div ng-include="'app/entities/cadastro-posto/cadastro/abas/abas-abastecimento/aba-historico.html'"
                            class="form-horizontal"> </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <div class="form-group">
                        <label class="col-xs-12 col-md-4 control-label"
                            style="text-align: right; padding-top: 10px;">Código da viagem:</label>
                        </label>
                        <div class="input-group col-xs-12 col-md-8">
                            <input type="text" ng-required="true" ng-disabled="true" ng-model="vm.codViagem"
                                class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <hr-label dark="true" title="'Pagamentos'"></hr-label>
        <br><br>
        <div class="row">
            <div class="col-lg-12">
                <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid"
                    style="width: 100%;" ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize
                    ui-grid-resize-columns ui-grid-grouping>
                </div>
            </div>
        </div>
    </div>
</div>