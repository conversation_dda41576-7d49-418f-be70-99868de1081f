<div>
    <style>
        .custom-modal-width .modal-dialog {
            width: 80%;
            max-width: 1200px;
        }
        
        .modal-detalhes-transacao .form-group {
            margin-bottom: 20px;
        }

        .modal-detalhes-transacao .field-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            font-size: 13px;
        }

        .modal-detalhes-transacao .field-input {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 14px;
        }

        .modal-detalhes-transacao .json-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .modal-detalhes-transacao .json-label {
            font-weight: 600;
            color: #056233;
            margin-bottom: 10px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .modal-detalhes-transacao .json-textarea {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            resize: vertical;
            min-height: 300px;
            transition: border-color 0.3s ease;
        }

        .modal-detalhes-transacao .json-textarea:hover {
            border-color: #056233;
            cursor: pointer;
        }

        .modal-detalhes-transacao .info-section {
            background-color: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .modal-detalhes-transacao .section-title {
            font-size: 16px;
            font-weight: 700;
            color: #056233;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .modal-detalhes-transacao .cancelamento-section {
            border-left: 4px solid #dc3545;
            background-color: #fff5f5;
        }
    </style>
    <form name="formPortador" novalidate show-validation>
        <div class="modal-content modal-detalhes-transacao">
            <div class="modal-header">
                <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
                    <em class="fa fa-times"></em>
                </button>
                <h4 class="modal-title">
                    <i class="fa fa-exchange"></i> Detalhes da Transação
                </h4>
            </div>
            <div class="modal-body">

                <div class="info-section">
                    <div class="section-title">
                        <i class="fa fa-info-circle"></i> Informações Básicas
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="field-label">Código da Transação</label>
                                <input type="text" ng-disabled="true" ng-model="vm.modal.id"
                                       class="form-control field-input" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="field-label">Código Response Dock</label>
                                <input type="text" ng-disabled="true" ng-model="vm.modal.idResponseDock"
                                       class="form-control field-input" />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="field-label">Data/Hora Requisição</label>
                                <input type="text" ng-disabled="true" ng-model="vm.modal.dataHoraRequisicao"
                                       class="form-control field-input" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="field-label">Data/Hora Resposta</label>
                                <input type="text" ng-disabled="true" ng-model="vm.modal.dataHoraResposta"
                                       class="form-control field-input" />
                            </div>
                        </div>
                    </div>
                </div>
               
                <div class="info-section">
                    <div class="section-title">
                        <i class="fa fa-code"></i> Dados da Transação
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="json-container">
                                <div class="json-label">
                                    <i class="fa fa-upload"></i>
                                    JSON Envio Dock
                                    <small ng-if="vm.modal.jsonEnvioDock" class="text-muted">(Clique para copiar)</small>
                                </div>
                                <a ng-click="vm.modal.jsonEnvioDock ? vm.copiar(1) : null">
                                    <textarea
                                        ng-disabled="!vm.modal.jsonEnvioDock"
                                        tooltip-placement="left"
                                        uib-tooltip="{{vm.modal.jsonEnvioDock ? 'Clique para copiar JSON de envio' : 'Nenhum JSON disponível'}}"
                                        readonly
                                        name="jsonEnvioDock"
                                        class="form-control json-textarea"
                                        placeholder="Nenhum JSON de envio disponível">{{ vm.modal.jsonEnvioDock ? (vm.jsonUtils.prettyPrint(vm.modal.jsonEnvioDock) || '') : '' }}</textarea>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="json-container">
                                <div class="json-label">
                                    <i class="fa fa-download"></i>
                                    JSON Resposta Dock
                                    <small ng-if="vm.modal.jsonRespostaDock" class="text-muted">(Clique para copiar)</small>
                                </div>
                                <a ng-click="vm.modal.jsonRespostaDock ? vm.copiar(2) : null">
                                    <textarea
                                        ng-disabled="!vm.modal.jsonRespostaDock"
                                        tooltip-placement="left"
                                        uib-tooltip="{{vm.modal.jsonRespostaDock ? 'Clique para copiar JSON de resposta' : 'Nenhum JSON disponível'}}"
                                        readonly
                                        name="jsonRespostaDock"
                                        class="form-control json-textarea"
                                        placeholder="Nenhum JSON de resposta disponível">{{ vm.modal.jsonRespostaDock ? (vm.jsonUtils.prettyPrint(vm.modal.jsonRespostaDock) || '') : '' }}</textarea>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div ng-show="vm.modal.status === 4" class="info-section cancelamento-section">
                    <div class="section-title">
                        <i class="fa fa-ban"></i> Dados do Cancelamento
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="field-label">Código Response Dock Cancelamento</label>
                                <input type="text" ng-disabled="true" ng-model="vm.modal.idResponseCancelamentoDock"
                                       class="form-control field-input" />
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="json-container">
                                <div class="json-label">
                                    <i class="fa fa-upload"></i>
                                    JSON Envio Dock Cancelamento
                                    <small ng-if="vm.modal.jsonEnvioDockCancelamento" class="text-muted">(Clique para copiar)</small>
                                </div>
                                <a ng-click="vm.modal.jsonEnvioDockCancelamento ? vm.copiar(3) : null">
                                    <textarea
                                        ng-disabled="!vm.modal.jsonEnvioDockCancelamento"
                                        tooltip-placement="left"
                                        uib-tooltip="{{vm.modal.jsonEnvioDockCancelamento ? 'Clique para copiar JSON de envio do cancelamento' : 'Nenhum JSON disponível'}}"
                                        readonly
                                        name="jsonEnvioDockCancelamento"
                                        class="form-control json-textarea"
                                        placeholder="Nenhum JSON de envio de cancelamento disponível">{{ vm.modal.jsonEnvioDockCancelamento ? (vm.jsonUtils.prettyPrint(vm.modal.jsonEnvioDockCancelamento) || '') : '' }}</textarea>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="json-container">
                                <div class="json-label">
                                    <i class="fa fa-download"></i>
                                    JSON Resposta Dock Cancelamento
                                    <small ng-if="vm.modal.jsonRespostaDockCancelamento" class="text-muted">(Clique para copiar)</small>
                                </div>
                                <a ng-click="vm.modal.jsonRespostaDockCancelamento ? vm.copiar(4) : null">
                                    <textarea
                                        ng-disabled="!vm.modal.jsonRespostaDockCancelamento"
                                        tooltip-placement="left"
                                        uib-tooltip="{{vm.modal.jsonRespostaDockCancelamento ? 'Clique para copiar JSON de resposta do cancelamento' : 'Nenhum JSON disponível'}}"
                                        readonly
                                        name="jsonRespostaDockCancelamento"
                                        class="form-control json-textarea"
                                        placeholder="Nenhum JSON de resposta de cancelamento disponível">{{ vm.modal.jsonRespostaDockCancelamento ? (vm.jsonUtils.prettyPrint(vm.modal.jsonRespostaDockCancelamento) || '') : '' }}</textarea>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>