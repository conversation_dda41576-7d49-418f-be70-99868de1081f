<div id="DetalhesTransacaoController" ng-controller="DetalhesTransacaoController as vm">
    <form-header items="vm.headerItems" head="'Detalhes da Transação'"></form-header>
    
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Detalhes da Transação #{{vm.transacaoId}}</h5>
                        <div ibox-tools></div>
                    </div>
                    
                    <div class="ibox-content">
                        <!-- Loading -->
                        <div ng-show="vm.carregandoTransacao" class="text-center">
                            <div class="spiner-example">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                    <div class="sk-rect6"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Dados da Transação -->
                        <div ng-show="!vm.carregandoTransacao">
                            <div class="row">
                                <div class="col-md-12">
                                    <button type="button" 
                                            class="btn btn-default pull-right" 
                                            ng-click="vm.voltarParaPagamento()"
                                            style="margin-left: 5px;">
                                        <i class="fa fa-arrow-left"></i> Voltar para Pagamento
                                    </button>
                                    <button type="button" 
                                            class="btn btn-default pull-right" 
                                            ng-click="vm.voltarParaLista()">
                                        <i class="fa fa-list"></i> Voltar para Lista
                                    </button>
                                    <h4>Informações da Transação</h4>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Código da Transação:</label>
                                        <p class="form-control-static">{{vm.transacao.id}}</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Código Resposta Dock:</label>
                                        <p class="form-control-static">{{vm.transacao.codigoRespostaDock || 'Não informado'}}</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Status:</label>
                                        <p class="form-control-static">
                                            <span ng-class="{
                                                'label label-success': vm.transacao.status === 'Baixado', 
                                                'label label-warning': vm.transacao.status === 'Pendente',
                                                'label label-danger': vm.transacao.status === 'NaoExecutado',
                                                'label label-default': vm.transacao.status === 'Cancelado'
                                            }">
                                                {{vm.transacao.statusDescricao}}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Valor:</label>
                                        <p class="form-control-static">
                                            <strong>{{vm.transacao.valor | currency:"R$ "}}</strong>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Data/Hora Requisição:</label>
                                        <p class="form-control-static">{{vm.transacao.dataHoraRequisicao}}</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Data/Hora Resposta:</label>
                                        <p class="form-control-static">{{vm.transacao.dataHoraResposta}}</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Id Conta Origem:</label>
                                        <p class="form-control-static">{{vm.transacao.idContaOrigem || 'Não informado'}}</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Id Conta Destino:</label>
                                        <p class="form-control-static">{{vm.transacao.idContaDestino || 'Não informado'}}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Data Baixa:</label>
                                        <p class="form-control-static">{{vm.transacao.dataBaixa || 'Não informado'}}</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Data Cadastro:</label>
                                        <p class="form-control-static">{{vm.transacao.dataCadastro}}</p>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- JSON Envio Dock -->
                            <div class="row">
                                <div class="col-md-12">
                                    <h4>JSON Envio Dock 
                                        <button type="button" 
                                                class="btn btn-xs btn-default" 
                                                ng-click="vm.copiarJson('envio')"
                                                ng-if="vm.transacao.jsonEnvio">
                                            <i class="fa fa-copy"></i> Copiar
                                        </button>
                                    </h4>
                                    
                                    <div ng-if="vm.transacao.jsonEnvio">
                                        <pre class="prettyprint lang-json" style="max-height: 300px; overflow-y: auto; background-color: #f5f5f5; padding: 10px; border: 1px solid #ddd;">{{vm.transacao.jsonEnvioFormatado}}</pre>
                                    </div>
                                    <div ng-if="!vm.transacao.jsonEnvio" class="alert alert-info">
                                        Não há JSON de envio disponível para esta transação.
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- JSON Resposta Dock -->
                            <div class="row">
                                <div class="col-md-12">
                                    <h4>JSON Resposta Dock 
                                        <button type="button" 
                                                class="btn btn-xs btn-default" 
                                                ng-click="vm.copiarJson('resposta')"
                                                ng-if="vm.transacao.jsonResposta">
                                            <i class="fa fa-copy"></i> Copiar
                                        </button>
                                    </h4>
                                    
                                    <div ng-if="vm.transacao.jsonResposta">
                                        <pre class="prettyprint lang-json" style="max-height: 300px; overflow-y: auto; background-color: #f5f5f5; padding: 10px; border: 1px solid #ddd;">{{vm.transacao.jsonRespostaFormatado}}</pre>
                                    </div>
                                    <div ng-if="!vm.transacao.jsonResposta" class="alert alert-info">
                                        Não há JSON de resposta disponível para esta transação.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
