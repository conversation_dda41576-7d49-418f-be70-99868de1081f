<div id="DetalhesTransacaoController" ng-controller="DetalhesTransacaoController as vm">
    <form-header items="vm.headerItems" head="'Detalhes da Transação'"></form-header>
    
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                   
                     <div class="ibox-title-2x" style="display: flex; align-items: center; justify-content: space-between; min-height: 50px; padding: 20px 20px 20px 20px;">
                        <div class="ibox-title-not-border">
                            <h5>Detalhes da Transação #{{vm.transacaoId}}</h5>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <button type="button"style="border-radius: 6px; font-weight: 500;" ng-disabled="vm.isSaving" ng-click="vm.voltarParaPagamento()" class="btn btn-labeled btn-default">
                                <span class="btn-label">
                                    <i class="fa fa-arrow-circle-left"></i>
                                </span>
                                Voltar
                            </button>

                            <button type="button"style="border-radius: 6px; font-weight: 500;" ng-disabled="vm.isSaving" ng-click="vm.voltarParaLista()" class="btn btn-labeled btn-default">
                                <span class="btn-label">
                                    <i class="fa fa-th-list"></i>
                                </span>
                                Pagamentos
                            </button>
                        </div>
                    </div>

                    
                    <div class="ibox-content">
                        <!-- Loading -->
                        <div ng-show="vm.carregandoTransacao" class="text-center">
                            <div class="spiner-example">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                    <div class="sk-rect6"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Dados da Transação -->
                        <div ng-show="!vm.carregandoTransacao">
                            <!-- Informações da Transação -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="card" style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.03); margin-bottom: 15px; border: 1px solid #e9ecef;">
                                        <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 12px 15px; border-radius: 8px 8px 0 0;">
                                            <h6 style="margin: 0; color: #495057; font-weight: 600;">
                                                <i class="fa fa-info-circle" style="color: #007bff; margin-right: 8px;"></i>
                                                Informações da Transação
                                            </h6>
                                        </div>
                                        <div class="card-body" style="padding: 15px;">
                                            <div class="row">
                                                <!-- Primeira linha: Dados principais -->
                                                <div class="col-md-3" style="margin-bottom: 12px;">
                                                    <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">ID Transação</label>
                                                    <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.transacao.id}}</div>
                                                </div>
                                                <div class="col-md-3" style="margin-bottom: 12px;">
                                                    <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">ID Response Dock</label>
                                                    <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.transacao.idResponseDock}}</div>
                                                </div>
                                                <div class="col-md-3" style="margin-bottom: 12px;">
                                                    <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">ID Response Cancelamento</label>
                                                    <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.transacao.idResponseCancelamentoDock}}</div>
                                                </div>
                                                <div class="col-md-3" style="margin-bottom: 12px;">
                                                    <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Status</label>
                                                    <span class="label" ng-class="{
                                                        'label-success': vm.transacao.statusDescricao === 'Fechado',
                                                        'label-warning': vm.transacao.statusDescricao === 'Pendente',
                                                        'label-danger': vm.transacao.statusDescricao === 'Erro',
                                                        'label-default': vm.transacao.statusDescricao === 'Cancelado',
                                                        'label-info': vm.transacao.statusDescricao === 'Processando'
                                                    }" style="font-size: 12px;">
                                                        {{vm.transacao.statusDescricao}}
                                                    </span>
                                                </div>

                                                <!-- Segunda linha: Datas -->
                                                <div ng-class="vm.transacao.dataHoraCancelamentoResposta ? 'col-md-4' : 'col-md-6'" style="margin-bottom: 12px;">
                                                    <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Data/Hora Requisição</label>
                                                    <div style="font-size: 14px; font-weight: 600; color: #495057;">{{vm.transacao.dataHoraRequisicao}}</div>
                                                </div>
                                                <div ng-class="vm.transacao.dataHoraCancelamentoResposta ? 'col-md-4' : 'col-md-6'">
                                                    <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Data/Hora Resposta</label>
                                                    <div style="font-size: 14px; font-weight: 600; color: #495057;">{{vm.transacao.dataHoraResposta}}</div>
                                                </div>
                                                <div class="col-md-4" ng-if="vm.transacao.dataHoraCancelamentoResposta">
                                                    <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Data/Hora Cancelamento</label>
                                                    <div style="font-size: 14px; font-weight: 600; color: #dc3545;">{{vm.transacao.dataHoraCancelamentoResposta}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Dados de Envio -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                                        <div style="display: flex; align-items: center; flex: 1; margin-right: 15px;">
                                            <h4 style="margin: 0; margin-right: 15px;">Dados de Envio</h4>
                                            <div style="flex: 1; height: 1px; background-color: #e9ecef;"></div>
                                        </div>
                                        <div style="display: flex; gap: 10px;">
                                            <button type="button"
                                                    style="border-radius: 6px; font-weight: 500;"
                                                    class="btn btn-sm"
                                                    ng-class="vm.modoVisualizacao.envio === 'cards' ? 'btn-primary' : 'btn-default'"
                                                    ng-click="vm.alternarVisualizacao('envio')"
                                                    ng-if="vm.transacao.jsonEnvio">
                                                <i class="fa" ng-class="vm.modoVisualizacao.envio === 'cards' ? 'fa-code' : 'fa-th-large'"></i>
                                                {{vm.modoVisualizacao.envio === 'cards' ? 'Ver JSON' : 'Ver Cards'}}
                                            </button>
                                            <button type="button"
                                                    class="btn btn-xs btn-default"
                                                    style="border-radius: 6px; font-weight: 500;"
                                                    ng-click="vm.copiarJson('envio')"
                                                    ng-if="vm.transacao.jsonEnvio">
                                                <i class="fa fa-copy"></i> Copiar JSON
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Visualização em Cards -->
                                    <div ng-if="vm.modoVisualizacao.envio === 'cards' && vm.transacao.jsonEnvio">
                                        <div class="row">
                                            <!-- Card: Dados da Transação -->
                                            <div class="col-md-6">
                                                <div class="card" style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.03); margin-bottom: 15px; border: 1px solid #e9ecef;">
                                                    <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 12px 15px; border-radius: 8px 8px 0 0;">
                                                        <h6 style="margin: 0; color: #495057; font-weight: 600;">
                                                            <i class="fa fa-exchange" style="color: #007bff; margin-right: 8px;"></i>
                                                            Dados da Transação
                                                        </h6>
                                                    </div>
                                                    <div class="card-body" style="padding: 15px;">
                                                        <div class="row">
                                                            <div class="col-md-6" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Conta Origem</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosEnvioCards.contaOrigem}}</div>
                                                            </div>
                                                            <div class="col-md-6" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Conta Destino</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosEnvioCards.contaDestino}}</div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Valor</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #28a745;">R$ {{vm.dadosEnvioCards.valor | number:2}}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Card: Informações de Descrição -->
                                            <div class="col-md-6" ng-if="vm.dadosEnvioCards.agencia || vm.dadosEnvioCards.documento">
                                                <div class="card" style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.03); margin-bottom: 15px; border: 1px solid #e9ecef;">
                                                    <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 12px 15px; border-radius: 8px 8px 0 0;">
                                                        <h6 style="margin: 0; color: #495057; font-weight: 600;">
                                                            <i class="fa fa-info-circle" style="color: #17a2b8; margin-right: 8px;"></i>
                                                            Informações de Descrição
                                                        </h6>
                                                    </div>
                                                    <div class="card-body" style="padding: 15px;">
                                                        <div class="row">
                                                            <div class="col-md-6" style="margin-bottom: 12px;" ng-if="vm.dadosEnvioCards.agencia">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Filial</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosEnvioCards.agencia}}</div>
                                                            </div>

                                                            <div class="col-md-6" style="margin-bottom: 12px;" ng-if="vm.dadosEnvioCards.documento">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Documento</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosEnvioCards.documento}}</div>
                                                            </div>
                                                            
                                                            <div class="col-md-12" ng-if="vm.dadosEnvioCards.protocolo">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Protocolo</label>
                                                                <div style="font-size: 14px; font-weight: 500; color: #495057;">{{vm.dadosEnvioCards.protocolo}}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Visualização JSON -->
                                    <div ng-if="vm.modoVisualizacao.envio === 'json' && vm.transacao.jsonEnvio">
                                        <pre class="prettyprint lang-json" style="max-height: 300px; overflow-y: auto; background-color: #f5f5f5; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">{{vm.transacao.jsonEnvioFormatado}}</pre>
                                    </div>

                                    <!-- Mensagem quando não há dados -->
                                    <div ng-if="!vm.transacao.jsonEnvio" class="alert alert-info">
                                        <i class="fa fa-info-circle"></i> Não há dados de envio disponíveis para esta transação.
                                    </div>
                                </div>
                            </div>

                            <!-- Dados de Resposta -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                                        <div style="display: flex; align-items: center; flex: 1; margin-right: 15px;">
                                            <h4 style="margin: 0; margin-right: 15px;">Dados de Resposta</h4>
                                            <div style="flex: 1; height: 1px; background-color: #e9ecef;"></div>
                                        </div>
                                        <div style="display: flex; gap: 10px;">
                                            <button type="button"
                                                    class="btn btn-sm"
                                                    style="border-radius: 6px; font-weight: 500;"
                                                    ng-class="vm.modoVisualizacao.resposta === 'cards' ? 'btn-primary' : 'btn-default'"
                                                    ng-click="vm.alternarVisualizacao('resposta')"
                                                    ng-if="vm.transacao.jsonResposta">
                                                <i class="fa" ng-class="vm.modoVisualizacao.resposta === 'cards' ? 'fa-code' : 'fa-th-large'"></i>
                                                {{vm.modoVisualizacao.resposta === 'cards' ? 'Ver JSON' : 'Ver Cards'}}
                                            </button>
                                            <button type="button"
                                                    style="border-radius: 6px; font-weight: 500;"
                                                    class="btn btn-xs btn-default"
                                                    ng-click="vm.copiarJson('resposta')"
                                                    ng-if="vm.transacao.jsonResposta">
                                                <i class="fa fa-copy"></i> Copiar JSON
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Visualização em Cards -->
                                    <div ng-if="vm.modoVisualizacao.resposta === 'cards' && vm.transacao.jsonResposta">
                                        <div class="row">
                                            <!-- Card: Dados da Transação -->
                                            <div class="col-md-6">
                                                <div class="card" style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.03); margin-bottom: 15px; border: 1px solid #e9ecef;">
                                                    <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 12px 15px; border-radius: 8px 8px 0 0;">
                                                        <h6 style="margin: 0; color: #495057; font-weight: 600;">
                                                            <i class="fa fa-check-circle" style="color: #28a745; margin-right: 8px;"></i>
                                                            Dados da Transação
                                                        </h6>
                                                    </div>
                                                    <div class="card-body" style="padding: 15px;">
                                                        <div class="row">
                                                            <div class="col-md-6" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Conta Origem</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCards.contaOrigem}}</div>
                                                            </div>
                                                            <div class="col-md-6" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Conta Destino</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCards.contaDestino}}</div>
                                                            </div>
                                                            <div class="col-md-6" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Valor</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #28a745;">R$ {{vm.dadosRespostaCards.valor | number:2}}</div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Data Transação</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCards.dataTransacao | date:'dd/MM/yyyy HH:mm'}}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Card: Informações Técnicas -->
                                            <div class="col-md-6">
                                                <div class="card" style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.03); margin-bottom: 15px; border: 1px solid #e9ecef;">
                                                    <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 12px 15px; border-radius: 8px 8px 0 0;">
                                                        <h6 style="margin: 0; color: #495057; font-weight: 600;">
                                                            <i class="fa fa-cogs" style="color: #6f42c1; margin-right: 8px;"></i>
                                                            Informações Técnicas
                                                        </h6>
                                                    </div>
                                                    <div class="card-body" style="padding: 15px;">
                                                        <div class="row">
                                                            <div class="col-md-12" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Código da Transação</label>
                                                                <div style="font-size: 12px; font-weight: 600; color: #495057; word-break: break-all; font-family: monospace; background: #f8f9fa; padding: 6px; border-radius: 4px;">{{vm.dadosRespostaCards.codigoTransacao}}</div>
                                                            </div>
                                                            <div class="col-md-4" style="margin-bottom: 8px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">ID Ajuste</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCards.idAjuste}}</div>
                                                            </div>
                                                            <div class="col-md-4" style="margin-bottom: 8px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">ID Emissor</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCards.idEmissor}}</div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">ID Ajuste Destino</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCards.idAjusteDestino}}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Visualização JSON -->
                                    <div ng-if="vm.modoVisualizacao.resposta === 'json' && vm.transacao.jsonResposta">
                                        <pre class="prettyprint lang-json" style="max-height: 300px; overflow-y: auto; background-color: #f5f5f5; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">{{vm.transacao.jsonRespostaFormatado}}</pre>
                                    </div>

                                    <!-- Mensagem quando não há dados -->
                                    <div ng-if="!vm.transacao.jsonResposta" class="alert alert-info">
                                        <i class="fa fa-info-circle"></i> Não há dados de resposta disponíveis para esta transação.
                                    </div>
                                </div>
                            </div>

                            <!-- Dados de Envio Cancelamento -->
                            <div class="row" ng-if="vm.transacao.jsonEnvioCancelamento">
                                <div class="col-md-12">
                                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                                        <div style="display: flex; align-items: center; flex: 1; margin-right: 15px;">
                                            <h4 style="margin: 0; margin-right: 15px;">Dados de Envio Cancelamento</h4>
                                            <div style="flex: 1; height: 1px; background-color: #e9ecef;"></div>
                                        </div>
                                        <div style="display: flex; gap: 10px;">
                                            <button type="button"
                                                    class="btn btn-sm"
                                                    style="border-radius: 6px; font-weight: 500;"
                                                    ng-class="vm.modoVisualizacao.cancelamentoEnvio === 'cards' ? 'btn-primary' : 'btn-default'"
                                                    ng-click="vm.alternarVisualizacao('cancelamento-envio')"
                                                    ng-if="vm.transacao.jsonEnvioCancelamento">
                                                <i class="fa" ng-class="vm.modoVisualizacao.cancelamentoEnvio === 'cards' ? 'fa-code' : 'fa-th-large'"></i>
                                                {{vm.modoVisualizacao.cancelamentoEnvio === 'cards' ? 'Ver JSON' : 'Ver Cards'}}
                                            </button>
                                            <button type="button"
                                                    style="border-radius: 6px; font-weight: 500;"
                                                    class="btn btn-xs btn-default"
                                                    ng-click="vm.copiarJson('cancelamento-envio')"
                                                    ng-if="vm.transacao.jsonEnvioCancelamento">
                                                <i class="fa fa-copy"></i> Copiar JSON
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Visualização em Cards -->
                                    <div ng-if="vm.modoVisualizacao.cancelamentoEnvio === 'cards' && vm.transacao.jsonEnvioCancelamento">
                                        <div class="row">
                                            <!-- Card: Dados de Envio Cancelamento -->
                                            <div class="col-md-6">
                                                <div class="card" style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.03); margin-bottom: 15px; border: 1px solid #e9ecef;">
                                                    <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 12px 15px; border-radius: 8px 8px 0 0;">
                                                        <h6 style="margin: 0; color: #495057; font-weight: 600;">
                                                            <i class="fa fa-ban" style="color: #dc3545; margin-right: 8px;"></i>
                                                            Dados da Transação
                                                        </h6>
                                                    </div>
                                                    <div class="card-body" style="padding: 15px;">
                                                        <div class="row">
                                                            <div class="col-md-6" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Conta Origem</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosEnvioCancelamentoCards.contaOrigem}}</div>
                                                            </div>
                                                            <div class="col-md-6" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Conta Destino</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosEnvioCancelamentoCards.contaDestino}}</div>
                                                            </div>
                                                            <div class="col-md-6" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Valor</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #dc3545;">R$ {{vm.dadosEnvioCancelamentoCards.valor | number:2}}</div>
                                                            </div>
                                                         
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Card: Informações de Descrição -->
                                            <div class="col-md-6" ng-if="vm.dadosEnvioCancelamentoCards.referencia">
                                                <div class="card" style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.03); margin-bottom: 15px; border: 1px solid #e9ecef;">
                                                    <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 12px 15px; border-radius: 8px 8px 0 0;">
                                                        <h6 style="margin: 0; color: #495057; font-weight: 600;">
                                                            <i class="fa fa-info-circle" style="color: #dc3545; margin-right: 8px;"></i>
                                                            Informações de Cancelamento
                                                        </h6>
                                                    </div>
                                                    <div class="card-body" style="padding: 15px;">
                                                        <div class="row">
                                                            <div class="col-md-12" style="margin-bottom: 12px;" ng-if="vm.dadosEnvioCards.agencia">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Referência</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosEnvioCancelamentoCards.referencia}}</div>
                                                            </div>
                                                            
                                                            <div class="col-md-12" ng-if="vm.dadosEnvioCards.protocolo">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Protocolo</label>
                                                                <div style="font-size: 15px; font-weight: 500; color: #495057;">{{vm.dadosEnvioCancelamentoCards.protocolo}}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Visualização JSON -->
                                    <div ng-if="vm.modoVisualizacao.cancelamentoEnvio === 'json' && vm.transacao.jsonEnvioCancelamento">
                                        <pre class="prettyprint lang-json" style="max-height: 300px; overflow-y: auto; background-color: #f5f5f5; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">{{vm.transacao.jsonEnvioCancelamentoFormatado}}</pre>
                                    </div>

                                    <!-- Mensagem quando não há dados -->
                                    <div ng-if="!vm.transacao.jsonEnvioCancelamento" class="alert alert-info">
                                        <i class="fa fa-info-circle"></i> Não há dados de envio de cancelamento disponíveis para esta transação.
                                    </div>
                                </div>
                            </div>

                            <!-- Dados de Resposta Cancelamento -->
                            <div class="row" ng-if="vm.transacao.jsonRespostaCancelamento">
                                <div class="col-md-12">
                                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                                        <div style="display: flex; align-items: center; flex: 1; margin-right: 15px;">
                                            <h4 style="margin: 0; margin-right: 15px;">Dados de Resposta Cancelamento</h4>
                                            <div style="flex: 1; height: 1px; background-color: #e9ecef;"></div>
                                        </div>
                                        <div style="display: flex; gap: 10px;">
                                            <button type="button"
                                                    class="btn btn-sm"
                                                    style="border-radius: 6px; font-weight: 500;"
                                                    ng-class="vm.modoVisualizacao.cancelamentoResposta === 'cards' ? 'btn-primary' : 'btn-default'"
                                                    ng-click="vm.alternarVisualizacao('cancelamento-resposta')"
                                                    ng-if="vm.transacao.jsonRespostaCancelamento">
                                                <i class="fa" ng-class="vm.modoVisualizacao.cancelamentoResposta === 'cards' ? 'fa-code' : 'fa-th-large'"></i>
                                                {{vm.modoVisualizacao.cancelamentoResposta === 'cards' ? 'Ver JSON' : 'Ver Cards'}}
                                            </button>
                                            <button type="button"
                                                    class="btn btn-xs btn-default"
                                                    style="border-radius: 6px; font-weight: 500;"
                                                    ng-click="vm.copiarJson('cancelamento-resposta')"
                                                    ng-if="vm.transacao.jsonRespostaCancelamento">
                                                <i class="fa fa-copy"></i> Copiar JSON
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Visualização em Cards -->
                                    <div ng-if="vm.modoVisualizacao.cancelamentoResposta === 'cards' && vm.transacao.jsonRespostaCancelamento">
                                        <div class="row">
                                            <!-- Card: Dados da Transação -->
                                            <div class="col-md-6">
                                                <div class="card" style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.03); margin-bottom: 15px; border: 1px solid #e9ecef;">
                                                    <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 12px 15px; border-radius: 8px 8px 0 0;">
                                                        <h6 style="margin: 0; color: #495057; font-weight: 600;">
                                                            <i class="fa fa-times-circle" style="color: #dc3545; margin-right: 8px;"></i>
                                                            Dados da Transação
                                                        </h6>
                                                    </div>
                                                    <div class="card-body" style="padding: 15px;">
                                                        <div class="row">
                                                            <div class="col-md-6" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Conta Origem</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCancelamentoCards.contaOrigem}}</div>
                                                            </div>
                                                            <div class="col-md-6" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Conta Destino</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCancelamentoCards.contaDestino}}</div>
                                                            </div>
                                                            <div class="col-md-6" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Valor</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #dc3545;">R$ {{vm.dadosRespostaCancelamentoCards.valor | number:2}}</div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Data Transação</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCancelamentoCards.dataTransacao | date:'dd/MM/yyyy HH:mm'}}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Card: Informações Técnicas -->
                                            <div class="col-md-6">
                                                <div class="card" style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.03); margin-bottom: 15px; border: 1px solid #e9ecef;">
                                                    <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 12px 15px; border-radius: 8px 8px 0 0;">
                                                        <h6 style="margin: 0; color: #495057; font-weight: 600;">
                                                            <i class="fa fa-cogs" style="color: #dc3545; margin-right: 8px;"></i>
                                                            Informações Técnicas
                                                        </h6>
                                                    </div>
                                                    <div class="card-body" style="padding: 15px;">
                                                        <div class="row">
                                                            <div class="col-md-12" style="margin-bottom: 12px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">Código da Transação</label>
                                                                <div style="font-size: 12px; font-weight: 600; color: #495057; word-break: break-all; font-family: monospace; background: #f8f9fa; padding: 6px; border-radius: 4px;">{{vm.dadosRespostaCancelamentoCards.codigoTransacao}}</div>
                                                            </div>
                                                            <div class="col-md-4" style="margin-bottom: 8px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">ID Ajuste</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCancelamentoCards.idAjuste}}</div>
                                                            </div>
                                                            <div class="col-md-4" style="margin-bottom: 8px;">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">ID Emissor</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCancelamentoCards.idEmissor}}</div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <label style="color: #6c757d; font-size: 11px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px; display: block;">ID Ajuste Destino</label>
                                                                <div style="font-size: 15px; font-weight: 600; color: #495057;">{{vm.dadosRespostaCancelamentoCards.idAjusteDestino}}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Visualização JSON -->
                                    <div ng-if="vm.modoVisualizacao.cancelamentoResposta === 'json' && vm.transacao.jsonRespostaCancelamento">
                                        <pre class="prettyprint lang-json" style="max-height: 300px; overflow-y: auto; background-color: #f5f5f5; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">{{vm.transacao.jsonRespostaCancelamentoFormatado}}</pre>
                                    </div>

                                    <!-- Mensagem quando não há dados -->
                                    <div ng-if="!vm.transacao.jsonRespostaCancelamento" class="alert alert-info">
                                        <i class="fa fa-info-circle"></i> Não há dados de resposta de cancelamento disponíveis para esta transação.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
