(function () {
    'user strict';
    angular.module('bbcWeb').factory('BaseService', BaseService);

    BaseService.$inject = ['$http', 'URL_SERVER_DEV', 'uiGridConstants', 'toastr', '$timeout', '$q', '$log', 'DefaultsService', '$window', '$rootScope'];

    function BaseService($http, URL_SERVER_DEV, uiGridConstants, toastr, $timeout, $q, $log, DefaultsService, $window, $rootScope) {
        var access = {
            headers: {
                "Content-Type": "application/json; charset=utf-8",
                "Accept": "application/json"
            }
        };

        var fn = {
            serializeObjectToQueryString: function (obj) {
                var str = [];
                for (var p in obj) {
                    if (obj.hasOwnProperty(p)) {
                        str.push(encodeURIComponent(p) + "=" + encodeURIComponent(obj[p]));
                    }
                }
                return str.join("&");
            },
            getCurrentUrl: function () {
                return URL_SERVER_DEV;
            },
            put: function (controller, method, params) {
                return $http.put(URL_SERVER_DEV + controller + '/' + method, params, access).then(function (response) {
                    return response.data;
                });
            },
            get: function (controller, method, params, canceller) {
                var parm = {
                    params: params
                };

                // if (angular.isDefined(responseType) && responseType !== null)
                //     parm.responseType = responseType;

                if (angular.isDefined(canceller) && canceller !== null)
                    parm.timeout = canceller.promise;

                return $http.get(URL_SERVER_DEV + controller + '/' + method, parm, access).then(function (response) {
                    return response.data;
                });
            },
            consulta: function (controller, method, params) {
                return $http.get(URL_SERVER_DEV + controller + '/' + method, {
                    params: params
                }, access).then(function (response) {
                    return response.data;
                })
            },
            post: function (controller, method, params) {
                return $http.post(URL_SERVER_DEV + controller + '/' + method, params, access).then(function (response) {
                    return response.data;
                });
            },
            //This method will avoid methods to been called twice,
            postAwareHttpRequest: function ($http, $q, requestConfig) {

                var uniqueRequestPropertyName = 'unique';
                var requestIdentifierPropertyName = 'requestId';

                function checkIfVaidateDuplicate(requestConfig) {
                    return !!requestConfig[requesrequestIdentifierPropertyNametConfig];
                }

                function checkIfHasDuplicate(requestConfig) {
                    var duplicates = $http.pendingRequests.filter(function (pendingRequestConfig) {
                        return (pendingRequestConfig[requestIdentifierPropertyName] && pendingRequestConfig[requestIdentifierPropertyName] === requestConfig[pendingRequestConfig[requestIdentifierPropertyName]]);
                    });
                    return duplicates.lenght > 0;
                }

                function requestBuilder(requestConfig) {
                    if (checkIfVaidateDuplicate(requestConfig) && checkIfHasDuplicate(requestConfig))
                        return;
                    return $http(requestConfig);
                }

                return requestBuilder;
            },
            postUnique: function (controller, method, params, requestId) {
                var requestConfig = {
                    url: URL_SERVER_DEV + controller + '/' + method,
                    data: params,
                    access: access,
                    unique: 1,
                    requestId: requestId
                };

                return postAwareHttpRequest(requestConfig).then(function (response) {
                    return response.data;
                });
            },
            download: function (data, contentType, fileName) {
                var file = b64toBlob(data, contentType);
                saveAs(file, fileName);
            },
            gerarToken: function (params) {
                return $http.post(URL_SERVER_DEV + 'AuthSession/GerarToken', params, access).then(function (response) {
                    return response.data;
                });
            },
            getDadosGrid: function (url, params) {
                return $http.get(URL_SERVER_DEV + url, {
                    params: params
                }, access).then(function (response) {
                    return response.data;
                });
            },
            pingWsServer: function (callback) {
                $http.get(URL_SERVER_DEV, {}, access).then(function () {
                    if (angular.isFunction(callback)) callback(true);
                }, function () {
                    $rootScope.alertBackEndNotResponding();
                    if (angular.isFunction(callback)) callback(false);
                });
            },
            getRuaPorLatLng: function (lat, lng) {
                return $http.get('https://maps.googleapis.com/maps/api/geocode/json', {
                    params: {
                        latlng: lat.toString() + ',' + lng.toString(),
                        key: 'AIzaSyCVJXzn9lupeEgWJee_sZ9NhBDxXID0OgQ'
                    }
                }).then(function (response) {
                    var retorno = {
                        rua: null,
                        numero: null
                    };

                    if (response && response.data && response.data.status !== "ZERO_RESULTS")
                        for (var i = 0; i < response.data.results[0].address_components.length; i++) {
                            for (var j = 0; j < response.data.results[0].address_components[i].types.length; j++) {
                                if (response.data.results[0].address_components[i].types[j] === "route")
                                    retorno.rua = response.data.results[0].address_components[i].long_name;
                                if (response.data.results[0].address_components[i].types[j] === "street_number")
                                    retorno.numero = response.data.results[0].address_components[i].long_name;
                            }
                        }

                    return retorno;
                });
            },
            getEnderecoLatLngExata: function (geocoder, lat, lng, callback) {
                geocoder.geocode({
                    'location': {
                        lat: lat,
                        lng: lng
                    }
                }, function (results, status) {
                    if (status === 'OK') {
                        if (results[0]) {
                            if (angular.isFunction(callback))
                                callback(results[0].formatted_address, results[0].geometry, results[0]);
                        } else if (angular.isFunction(callback))
                            callback(false);
                    } else {
                        if (angular.isFunction(callback))
                            callback(false);
                    }
                });
            },
            getEnderecoLatLng: function (geocoder, lat, lng, callback) {
                geocoder.geocode({
                    'location': {
                        lat: lat,
                        lng: lng
                    }
                }, function (results, status) {
                    if (status === 'OK') {
                        if (results[1]) {
                            if (angular.isFunction(callback))
                                callback(results[1].formatted_address, results[1].geometry);
                        } else if (angular.isFunction(callback))
                            callback(false);
                    } else {
                        if (angular.isFunction(callback))
                            callback(false);
                    }
                });
            },
            getEndereco: function (cep) {
                cep = cep.replace(/\.|\-/g, '');
                if (cep != null && cep != "") {
                    return $.get('https://viacep.com.br/ws/' + cep + '/json',
                        function (data) {
                            var retorno = {};
                            retorno.Bairro = data.bairro;
                            retorno.Cidade = data.localidade;
                            retorno.Estado = data.uf;
                            retorno.Logradouro = data.logradouro;
                            retorno.Complemento = data.complemento;

                            return retorno;
                        }
                    );
                }
                return;
            },
            downloadMediaByToken: function (mediaToken, callback) {
                fn.uploader.utils.getMediaContentByToken(mediaToken, callback);
            },
            // Upload...
            uploader: {
                fileUploadMaxSizeMb: 10,
                utils: {
                    getBase64FromFile: function (file, callback) {
                        if (!angular.isFunction(callback))
                            throw new Error('É obrigatório informar um callback do tipo "function"!');

                        var reader = new FileReader();
                        reader.readAsDataURL(file);
                        reader.onload = function () {
                            callback(true, reader.result);
                        };
                        reader.onerror = function (error) {
                            callback(false, error);
                        };
                    },
                    getMediaContentByToken: function (mediaToken, callback) {
                        if (!angular.isFunction(callback))
                            throw new Error('É obrigatório informar um callback!');

                        if (angular.isUndefined(mediaToken) || mediaToken == null || mediaToken.length === 0)
                            callback(false, null, 'É obrigatório informar um media token válido!');

                        fn.get('FileUploaderAts', 'Consultar', {
                            token: mediaToken
                        }).then(function (response) {
                            callback(response.success, response.data, response.message);
                        });
                    },
                    fileExceddedSizeLimit: function (file) {
                        var filesize = ((file.size / 1024) / 1024).toFixed(2); // MB
                        var validationResult = filesize > fn.uploader.fileUploadMaxSizeMb;
                        var message = null;

                        // Ultrapassou o limite máximo
                        if (validationResult)
                            message = "O arquivo <i>" + file.name + "(" + filesize + "Mb)</i> ultrapassou o tamanho máximo de " + fn.uploader.fileUploadMaxSizeMb + "Mb<br/> Selecione outro arquivo!";

                        return {
                            excedded: validationResult,
                            message: message
                        };
                    }
                },
                uploadFile: function (file, callback) {
                    if (!angular.isFunction(callback))
                        throw new Error('É obrigatório informar um callback!');

                    if (angular.isUndefined(file) || file == null)
                        callback(false, null, 'É necessário informar um arquivo a ser feito o upload!');

                    //Validação de tamanho máximo permitido para upload
                    var maxSizeValidation = fn.uploader.utils.fileExceddedSizeLimit(file);
                    if (maxSizeValidation.excedded)
                        callback(false, null, maxSizeValidation.message);
                    else
                        fn.uploader.utils.getBase64FromFile(file, function (success, content) {
                            if (success)
                                fn.post('FileUploaderAts', 'Upload', {
                                    base64: content,
                                    fileName: file.name
                                }).then(function (response) {
                                    callback(response.success, response.data, response.message);
                                });
                            else
                                $log.error('Impossível de ler o conteúdo do arquivo a ser feito upload!');
                        });
                },
                uploadBase64: function (fileBase64Content, callback) {
                    // Este método não conta com a validação do tamanho máximo permitido para enviar um arquivo...
                    // este recurso está apenas disponível no método de uploadFile
                    if (!angular.isFunction(callback))
                        throw new Error('É obrigatório informar um callback!');

                    if (angular.isUndefined(fileBase64Content) || fileBase64Content == null || fileBase64Content.length === 0)
                        callback(false, null, 'É necessário informar o conteúdo do arquivo a ser feito o upload!');

                    fn.post('FileUploaderAts', 'Upload', {
                        base64: fileBase64Content
                    }).then(function (response) {
                        callback(response.success, response.data, response.message);
                    });
                },
                downloadFileByToken: function (mediaToken, callback) {
                    if (!angular.isFunction(callback))
                        throw new Error('É obrigatório informar um callback!');

                    if (angular.isUndefined(mediaToken) || mediaToken == null || mediaToken.length === 0)
                        callback(false, 'É obrigatório informar um media token válido!');

                    fn.uploader.utils.getMediaContentByToken(mediaToken, function (success, data, message) {
                        if (success) {
                            fn.download(data.Data, data.BlobType, data.FileName);
                            callback(true, 'Download realizado com sucesso');
                        } else callback(false, message);
                    });
                }
            },
            // Grid Default
            dataGrid: {
                getGridApiRegistration: function (gridName, callback) {
                    return function (gridApi) {
                        var scope = gridApi.grid.appScope.vm;
                        scope[gridName + "Api"] = gridApi;
                        var gridApiF = scope[gridName + "Api"];

                        // Caso a grid tenha lastState, então chamamos o 'restore'
                        var lastState = scope[gridName + "Api"].lastState;
                        if (angular.isDefined(lastState))
                            scope[gridName + "Api"].saveState.restore(scope, lastState);

                        // Tratamento de DEFAULTS
                        // Grid menu
                        if (angular.isUndefined(scope[gridName].enableGridMenu))
                            scope[gridName].enableGridMenu = true;

                        if (angular.isUndefined(scope[gridName].data))
                            scope[gridName].data = [];

                        scope[gridName].getGridHeight = function () {
                            var minumoRows = 10;
                            var rowsLen = scope[gridName].data.length;
                            return ((((rowsLen < minumoRows ? minumoRows : rowsLen)) * 30) + 150 + 'px');
                        };

                        scope.toggleColumnFooter = function () {
                            scope.gridOptions.showColumnFooter = !scope.gridOptions.showColumnFooter;
                            scope.gridApi.core.notifyDataChange(uiGridConstants.dataChange.ALL);
                        };

                        // Filtro na grid
                        // Forçando para true
                        if (angular.isUndefined(scope[gridName].enableFiltering) || scope[gridName].enableFiltering == null)
                            scope[gridName].enableFiltering = true;

                        scope[gridName].useExternalSorting = true;
                        scope[gridName].useExternalFiltering = true;
                        scope[gridName].enableSorting = true;
                        scope[gridName].enableRowHashing = true;

                        if (angular.isUndefined(scope[gridName].paginationPageSizes) ||
                            scope[gridName].paginationPageSizes.toString() === [250, 500, 1000].toString())
                            scope[gridName].paginationPageSizes = [10, 25, 50, 100, 250, 500];

                        // Items por página
                        if (angular.isUndefined(scope[gridName].paginationPageSize) ||
                            scope[gridName].paginationPageSize === 250)
                            scope[gridName].paginationPageSize = 10;

                        // Página atual
                        if (angular.isUndefined(scope[gridName].paginationCurrentPage))
                            scope[gridName].paginationCurrentPage = 1;

                        // Força sempre usar paginação externa
                        scope[gridName].useExternalPagination = true;

                        scope[gridName].columnDefs.forEach(function (item) {
                            // Aggregates menu
                            if (!item.groupingShowAggregationMenu)
                                item.groupingShowAggregationMenu = false;
                            // Defaults para PK
                            if (item.primaryKey && angular.isUndefined(item.serverField)) {
                                item.enableFiltering = true;
                                item.enableGrouping = false;
                                if (angular.isUndefined(item.width))
                                    item.width = "5%";
                            }

                            if (item.type === "number" || item.primaryKey)
                                item.cellClass = "rAlign";

                            if (angular.isUndefined(item.field) || item.name === 'Ações') {
                                item.enableGrouping = false;
                                item.enableSorting = false;
                                item.enableFiltering = false;
                                item.enableColumnMenu = false;

                                if (item.name === 'Ações') {
                                    item.cellTemplate = "<center>" + item.cellTemplate + "</center>";
                                    // item.pinnedRight = true;
                                    // item.pinned = true;
                                    // item.width = "*";
                                }
                            }

                            // largura...
                            if (angular.isUndefined(item.width)) item.width = "15%";

                            if (angular.isUndefined(item.headerCellClass))
                                item.headerCellClass = highlightFilteredHeader;

                            if (angular.isUndefined(item.enableFiltering) || item.enableFiltering === null &&
                                (angular.isDefined(item.field) || angular.isDefined(item.serverField)))
                                item.enableFiltering = true;

                            if (item.sel) {
                                item.enableFiltering = false;
                                item.enableGrouping = false;
                                item.enableSorting = false;
                                item.cellTemplate = fn.dataGrid.getSelTemplate();
                            }

                            scope[gridName + "Api"].grid.getCurrentModel = function () {
                                //create a ramdom name...
                                return 'grid.appScope.vm.select' + (Math.random(0, 100000000) * 100).toString().split('.')[0];
                            }

                            if (item.enum && item.enableFiltering) {

                                if (angular.isUndefined(item.filters))
                                    item.filters = [{
                                        type: uiGridConstants.filter.SELECT
                                    }];

                                if (angular.isDefined(item.enumTipo)) {
                                    if (item.enumTipo === 'ESimNao') {
                                        item.filters[0].selectOptions = DefaultsService.enums.ESimNao.get();
                                    } else if (item.enumTipo === 'EMensagensTratadasNaoTratadas') {
                                        item.filters[0].selectOptions = DefaultsService.enums.EMensagensTratadasNaoTratadas.get();
                                    } else if (item.enumTipo === 'EAprovadoReprovado') {
                                        item.filters[0].selectOptions = DefaultsService.enums.EAprovadoReprovado.get();
                                    } else if (item.enumTipo === 'EStatusProtocoloEvento_SemRejeiado') {
                                        var enumProtocolo = DefaultsService.enums.EStatusProtocoloEvento_SemRejeiado.get();
                                        item.filters[0].selectOptions = enumProtocolo;
                                    } else if (item.enumTipo === 'EStatusOcorrencia') {
                                        var enumOcorrencia = DefaultsService.enums.EStatusOcorrencia.get();
                                        item.filters[0].selectOptions = enumOcorrencia;
                                    } else if (item.enumTipo === 'EStatusCadastros') {
                                        var enumStatusCadastros = DefaultsService.enums.EStatusCadastros.get();
                                        item.filters[0].selectOptions = enumStatusCadastros;
                                    } else if (item.enumTipo === 'ETipoPagamentos') {
                                        var enumTipoPagamentos = DefaultsService.enums.ETipoPagamentos.get();
                                        item.filters[0].selectOptions = enumTipoPagamentos;
                                    } else if (item.enumTipo === 'EProcessoVinculados') {
                                        var enumProcessoVinculados = DefaultsService.enums.EProcessoVinculados.get();
                                        item.filters[0].selectOptions = enumProcessoVinculados;
                                    } else if (item.enumTipo === 'EFormaPagamentos') {
                                        var enumFormaPagamentos = DefaultsService.enums.EFormaPagamentos.get();
                                        item.filters[0].selectOptions = enumFormaPagamentos;
                                    } else if (item.enumTipo === 'EStatusPagamentos') {
                                        var enumStatusPagamentos = DefaultsService.enums.EStatusPagamentos.get();
                                        item.filters[0].selectOptions = enumStatusPagamentos;
                                    } else if (item.enumTipo === 'ETipoEmissaoCIOT') {
                                        var enumTipoEmissaoCIOT = DefaultsService.enums.ETipoEmissaoCIOT.get();
                                        item.filters[0].selectOptions = enumTipoEmissaoCIOT;
                                    } else if (item.enumTipo === 'ETipoMensagem') {
                                        var enumTipoMensagem = DefaultsService.enums.ETipoMensagem.get();
                                        item.filters[0].selectOptions = enumTipoMensagem;
                                    } else if (item.enumTipo === 'ECodigoAplicacao') {
                                        var enumCodigoAplicacao = DefaultsService.enums.ECodigoAplicacao.get();
                                        item.filters[0].selectOptions = enumCodigoAplicacao;
                                    } else if (item.enumTipo === 'ETipoCIOT') {
                                        var enumTipoCiot = DefaultsService.enums.ETipoCIOT.get();
                                        item.filters[0].selectOptions = enumTipoCiot;
                                    } else if (item.enumTipo === 'ETipoEvento') {
                                        var enumTipoCiot = DefaultsService.enums.ETipoEvento.get();
                                        item.filters[0].selectOptions = enumTipoCiot;
                                    } else if (item.enumTipo === 'EFormaPagamentoEvento') {
                                        var enumFormaPagamentoEvento = DefaultsService.enums.EFormaPagamentoEvento.get();
                                        item.filters[0].selectOptions = enumFormaPagamentoEvento;
                                    } else if (item.enumTipo === 'EStatusTransacao') {
                                        var enumTipoCiot = DefaultsService.enums.EStatusTransacao.get();
                                        item.filters[0].selectOptions = enumTipoCiot;
                                    }else if (item.enumTipo === 'EStatusCredenciamentoPosto') {
                                        var enumTipoCiot = DefaultsService.enums.EStatusCredenciamentoPosto.get();
                                        item.filters[0].selectOptions = enumTipoCiot;
                                    }else if (item.enumTipo === 'EFarolSla') {
                                        var enumTipoCiot = DefaultsService.enums.EFarolSla.get();
                                        item.filters[0].selectOptions = enumTipoCiot;
                                    } else if (item.enumTipo === 'EMetodoAbastecimento') {
                                        var enumTipoCiot = DefaultsService.enums.EMetodoAbastecimento.get();
                                        item.filters[0].selectOptions = enumTipoCiot;
                                    } else if (item.enumTipo === 'EStatusNotificacoes') {
                                        var enumTipoCiot = DefaultsService.enums.EStatusNotificacoes.get();
                                        item.filters[0].selectOptions = enumTipoCiot;
                                    } else if (item.enumTipo === 'EStatusCIOT') {
                                        var enumStatusCiot = DefaultsService.enums.EStatusCIOT.get();
                                        item.filters[0].selectOptions = enumStatusCiot;
                                    } else if (item.enumTipo === 'EStatusTipoOperacao') {
                                        var enumStatusTipoOperacao = DefaultsService.enums.EStatusTipoOperacao.get();
                                        item.filters[0].selectOptions = enumStatusTipoOperacao;
                                    } else if (item.enumTipo === 'EStatusAutorizacaoAbastecimento') {
                                        var enumStatusAutorizacaoAbastecimento = DefaultsService.enums.EStatusAutorizacaoAbastecimento.get();
                                        item.filters[0].selectOptions = enumStatusAutorizacaoAbastecimento;
                                    } else if (item.enumTipo === 'EStatusPagamentoPedagio') {
                                        var enumStatusPagamentoPedagio = DefaultsService.enums.EStatusPagamentoPedagio.get();
                                        item.filters[0].selectOptions = enumStatusPagamentoPedagio;
                                    } else if (item.enumTipo === 'EStatusViagem') {
                                            var enumStatusViagem = DefaultsService.enums.EStatusViagem.get();
                                            item.filters[0].selectOptions = enumStatusViagem;
                                    } else if (item.enumTipo === 'EStatusPagamentoPedagioConsultaCentralPendencias') {
                                        var enumStatusPagamentoPedagioConsultaCentralPendencias = DefaultsService.enums.EStatusPagamentoPedagioConsultaCentralPendencias.get();
                                        item.filters[0].selectOptions = enumStatusPagamentoPedagioConsultaCentralPendencias;
                                    } else if (item.enumTipo === 'EFormaPagamentoPedagio') {
                                        var enumFormaPagamentoPedagio = DefaultsService.enums.EFormaPagamentoPedagio.get();
                                        item.filters[0].selectOptions = enumFormaPagamentoPedagio;
                                    } else if (item.enumTipo === 'EStatusPagamentoEvento') {
                                        var enumStatusPagamentoEvento = DefaultsService.enums.EStatusPagamentoEvento.get();
                                        item.filters[0].selectOptions = enumStatusPagamentoEvento;
                                    } else if (item.enumTipo === 'ETipoContaDock') {
                                        var enumTipoContaDock = DefaultsService.enums.ETipoContaDock.get();
                                        item.filters[0].selectOptions = enumTipoContaDock;
                                    } else if (item.enumTipo === 'EStatusServidorCiot') {
                                        var enumStatusServidorCiot = DefaultsService.enums.EStatusServidorCiot.get();
                                        item.filters[0].selectOptions = enumStatusServidorCiot;
                                    } else if (item.enumTipo === 'EStatusComunicacaoAntt') {
                                        var enumStatusComunicacaoAntt = DefaultsService.enums.EStatusComunicacaoAntt.get();
                                        item.filters[0].selectOptions = enumStatusComunicacaoAntt;
                                    } else if (item.enumTipo === 'ETipoServidor') {
                                        var enumTipoServidor = DefaultsService.enums.ETipoServidor.get();
                                        item.filters[0].selectOptions = enumTipoServidor;
                                    } else if (item.enumTipo === 'EContingenciaCiot') {
                                        var enumContingenciaCiot = DefaultsService.enums.EContingenciaCiot.get();
                                        item.filters[0].selectOptions = enumContingenciaCiot;
                                    } else if (item.enumTipo === 'ECiotEncerrado') {
                                        var enumCiotEncerrado = DefaultsService.enums.ECiotEncerrado.get();
                                        item.filters[0].selectOptions = enumCiotEncerrado;
                                    } else if (item.enumTipo === 'EVersaoIntegracao') {
                                        var enumVersaIntegracaoPagamento = DefaultsService.enums.EVersaoIntegracao.get();
                                        item.filters[0].selectOptions = enumVersaIntegracaoPagamento;
                                    } else if (item.enumTipo === 'EStatusPortador') {
                                        var enumStatusPortador = DefaultsService.enums.EStatusPortador.get();
                                        item.filters[0].selectOptions = enumStatusPortador;
                                    } else
                                        DefaultsService.enums.get(item.enumTipo).then(function (retorno) {
                                            item.filters[0].selectOptions = retorno.data;
                                        });
                                } else {
                                    if (item.enumData)
                                        item.filters[0].selectOptions = item.enumData;
                                    else {
                                        if (item.enumOptions) {
                                            if (!item.enumOptions.url)
                                                $log.warn("ATS Grid - Error: Não é possível carregar uma select sem url[" + item.field + "]");
                                            else if (!item.enumOptions.type)
                                                $log.warn("ATS Grid - Error: Não é possível carregar uma select com uma URL sem type[" + item.field + "]");
                                            else if (!item.enumOptions.responseDataField)
                                                $log.warn("ATS Grid - Error: Não é possível carregar uma select sem responseDataField[" + item.field + "]");
                                            else
                                                $http[item.enumOptions.type.toLowerCase()]
                                                (URL_SERVER_DEV + item.enumOptions.url, item.enumOptions.params, access).then(function (response) {
                                                    var items = response.data.data[item.enumOptions.responseDataField];
                                                    if (angular.isDefined(items))
                                                        items.forEach(function (i) {
                                                            if (i.label.length > 35) i.label = i.label.slice(0, 35) + "...";
                                                        });

                                                    item.filters[0].selectOptions = items;
                                                });
                                        } else
                                            $log.warn('GridOverFlow: Não foi possível carregar dados para o filtro ' + item.field);
                                    }
                                }

                                if (angular.isDefined(item.selectedFilter)) {
                                    item.filters[0].term = item.filters[0].selectOptions[item.selectedFilter].value;
                                }
                            }

                            if (item.type === 'date' && item.enableFiltering) {
                                item.filterHeaderTemplate = 'customDateFieldTemplateUiGrid',
                                    item.filters = [{
                                        condition: uiGridConstants.filter.GREATER_THAN_OR_EQUAL,
                                        placeholder: 'Maior igual á',
                                        type: 'date'
                                    }, {
                                        condition: uiGridConstants.filter.LESS_THAN_OR_EQUAL,
                                        placeholder: 'Menor igual que',
                                        type: 'date'
                                    }];
                            }

                            // Coloca agrupamento em todas colunas que tem serverField e 
                            // o enableGrouping seja indefinido
                            if (angular.isDefined(item.serverField) && angular.isUndefined(item.enableGrouping) && !item.primaryKey)
                                item.enableGrouping = true;

                            if (angular.isUndefined(item.enableGrouping))
                                item.enableGrouping = false;

                            if (item.name === 'Ações')
                                item.minWidth = 70;
                        });
                        // Fim 

                        if (angular.isDefined(scope[gridName + "Api"].pagination))
                            scope[gridName + "Api"].pagination.on.paginationChanged(null, function (newPage, pageSize) {
                                loadData(newPage, pageSize);
                                $timeout(function () {
                                    $window.dispatchEvent(new Event('resize'));
                                }, 500);
                            });

                        var filtrosPorColuna = [];

                        scope[gridName + "Api"].core.on.filterChanged(null, function () {
                            realizarFiltro(this.grid);
                            $timeout(function () {
                                $window.dispatchEvent(new Event('resize'));
                            }, 500);
                        });

                        function realizarFiltro(grid) {
                            // IsEqual = 0,
                            // Contains = 1,
                            // HigherThan = 2,
                            // LowerThan = 3

                            // var grid = grid;
                            filtrosPorColuna = [];
                            gridApi.grid.options.filtrosPorColuna = [];
                            grid.columns.forEach(function (item) {
                                item.filters.forEach(function (filtro) {
                                    if (angular.isDefined(filtro) && angular.isDefined(filtro.term) && filtro.term !== null)
                                        if ((item.colDef.serverField || item.colDef.field) && filtro.term !== null && filtro.term !== "") {
                                            filtrosPorColuna.push({
                                                Campo: item.colDef.serverField || item.colDef.field,
                                                Operador: filtro.condition || uiGridConstants.filter.CONTAINS,
                                                Valor: filtro.term,
                                                ServerFieldCollection: item.colDef.serverFieldCollection,
                                                CampoTipo: item.colDef.type === 'date' ? 0 : (item.colDef.type === 'number' || item.colDef.enum ? 2 : 1)
                                            });
                                            gridApi.grid.options.filtrosPorColuna.push({
                                                Campo: item.colDef.serverField || item.colDef.field,
                                                Operador: filtro.condition || uiGridConstants.filter.CONTAINS,
                                                Valor: filtro.term,
                                                ServerFieldCollection: item.colDef.serverFieldCollection,
                                                CampoTipo: item.colDef.type === 'date' ? 0 : (item.colDef.type === 'number' || item.colDef.enum ? 2 : 1)
                                            });
                                        }
                                })
                            });

                            loadData(gridApi.grid.options.paginationCurrentPage, gridApi.grid.options.paginationPageSize);
                        }

                        if (gridApi.grid.options.dataSource &&
                            (gridApi.grid.options.dataSource.autoBind || angular.isUndefined(gridApi.grid.options.dataSource.autoBind))) {
                            // Habilita paginação externa...
                            scope[gridName].useExternalPagination = true;
                            // Se já tiver algum dado, então não chamamos o dt, msm sendo autoBind true...
                            if (scope[gridName].data.length === 0) {
                                $timeout(function () {
                                    realizarFiltro(gridApi.grid);
                                    loadData(1, gridApi.grid.options.paginationPageSize);
                                }, 250);
                            }
                        }

                        // When: Sorting start...
                        scope[gridName + "Api"].core.on.sortChanged(null, sortChanged);

                        var lastSortUsed;
                        gridApi.grid.options.lastSortUsed = undefined;

                        function sortChanged(grid, sortColumns) {
                            var coluna = sortColumns[sortColumns.length - 1];
                            if (sortColumns.length === 0 || angular.isUndefined(sortColumns)) {
                                lastSortUsed = undefined;
                                loadData(1, gridApi.grid.options.paginationPageSize);
                            } else {
                                lastSortUsed = {
                                    Campo: (coluna.colDef.serverField ? coluna.colDef.serverField : coluna.field) || getFieldPrimaryKey(),
                                    Operador: coluna.sort.direction === uiGridConstants.DESC ? 1 : 0
                                };
                                gridApi.grid.options.lastSortUsed = lastSortUsed;
                                loadData(gridApi.grid.options.paginationCurrentPage, gridApi.grid.options.paginationPageSize, lastSortUsed);
                            }

                            $timeout(function () {
                                $window.dispatchEvent(new Event('resize'));
                            }, 500);
                        }

                        function getFieldPrimaryKey() {
                            var retorno = null;
                            gridApi.grid.options.columnDefs.map(function (x) {
                                if (x.primaryKey) retorno = x.field;
                            });

                            return retorno;
                        }

                        function highlightFilteredHeader(row, rowRenderIndex, col) {
                            if (col.filters[0].term)
                                return 'header-filtered';
                            else return '';
                        }

                        function loadData(newPage, pageSize, sortCfg) {
                            $timeout(function () {
                                $window.dispatchEvent(new Event('resize'));
                            }, 500);

                            scope[gridName].data.length = 0;

                            scope[gridName].virtualizationThreshold = pageSize;
                            scope[gridName].loading = true;

                            var dtSrc = scope[gridName + "Api"].grid.options.dataSource;
                            var params = angular.isFunction(dtSrc.params) ? dtSrc.params() : {};
                            var queryFiltersOptionalFnRet = angular.isFunction(dtSrc.queryFiltersOptionalFn) ? dtSrc.queryFiltersOptionalFn() : [];


                            params.Page = newPage || 1;
                            params.Take = pageSize || gridApi.grid.options.paginationPageSize;

                            if (sortCfg || lastSortUsed)
                                params.Order = {
                                    Campo: sortCfg ? sortCfg.Campo : lastSortUsed.Campo,
                                    Operador: sortCfg ? sortCfg.Operador : lastSortUsed.Operador
                                };
                            else {
                                var pk = getFieldPrimaryKey();
                                if (!pk || angular.isUndefined(pk) || pk === null)
                                    $log.info('Não foi possível encontrar uma primary key para esta tabela, não haverá ordenamento padrão');
                                else
                                    params.Order = {
                                        Campo: pk,
                                        Operador: 1
                                    };
                            }
                            if (angular.isUndefined(params.Filters) || params.Filters == null)
                                params.Filters = [];

                            // Caso haja 
                            if (angular.isObject(filtrosPorColuna) && filtrosPorColuna.length > 0)
                                if (angular.isArray(filtrosPorColuna))
                                    filtrosPorColuna.forEach(function (xxyy) {
                                        params.Filters.push(xxyy);
                                    });

                            // Roda o queryFiltersOptionalFnRet..
                            if (queryFiltersOptionalFnRet.length > 0)
                                queryFiltersOptionalFnRet.forEach(function (ref) {
                                    params.Filters.push(ref);
                                });

                            // Busca os dados no servidor. sim... é POST e não GET, GET não aceito objetos na requisição
                            $http.post(URL_SERVER_DEV + scope[gridName + "Api"].grid.options.dataSource.url, params, access).then(function (response) {
                                if (response.data && response.data.data && response.data.data.items &&
                                    response.data.data.items.length > 0) {
                                    scope[gridName].data = response.data.data.items;
                                    scope[gridName].totalItems = response.data.data.totalItems;
                                    scope[gridName].totalizador = response.data.data.totalizador;
                                    scope[gridName].totalPgtos = response.data.data.totalPgtos;
                                    if (angular.isFunction(scope[gridName + "Api"].grid.options.callBack))
                                        scope[gridName + "Api"].grid.options.callBack(response.data);
                                } else {
                                    scope[gridName].data = [];
                                    scope[gridName].totalItems = 0;
                                }

                                if (!response.data.success) {
                                    //toastr
                                    if (dtSrc.exceptions) {
                                        if (dtSrc.exceptions.onReturnNotSuccess && angular.isFunction(dtSrc.exceptions.onReturnNotSuccess))
                                            dtSrc.exceptions.onReturnNotSuccess(response.data.message);
                                    }
                                }

                                if (dtSrc.events) {
                                    if (angular.isFunction(dtSrc.events.onDataBound)) {
                                        dtSrc.events.onDataBound(scope[gridName].data);
                                    }
                                }
                            }).finally(function () {
                                // Remove o gif de loading...
                                scope[gridName].loading = false;
                                // Salva o estado da grid...
                                scope[gridName].lastState = scope[gridName + "Api"].saveState.save();

                                $timeout(function () {
                                    $window.dispatchEvent(new Event('resize'));
                                }, 500);
                            });
                        }

                        // Método para recarregar a grid..
                        scope[gridName + "Api"].grid.options.dataSource.refresh = function () {
                            var scopeInternal = gridApiF.grid.appScope.vm;
                            if (scopeInternal[gridName].paginationCurrentPage === 1)
                                loadData(1, scopeInternal[gridName + "Api"].grid.options.paginationPageSize, null);
                            else
                                scopeInternal[gridName + "Api"].pagination.seek(1);
                        };

                        scope[gridName].dataSource.consultarDadosRelatorio = function (callBack, gridName) {
                            
                            $timeout(function () {
                                $window.dispatchEvent(new Event('resize'));
                            }, 600);

                            if (angular.isUndefined(scope[gridName].urlRelatorio))
                                return;

                            var dataSource = scope[gridName].dataSource;
                            var params = angular.isFunction(dataSource.params) ? dataSource.params() : {};
                            var queryFiltersOptionalFn = angular.isFunction(dataSource.queryFiltersOptionalFn) ? dataSource.queryFiltersOptionalFn() : [];

                    
                            params.Page = 1;
                            if (gridName == "gridOptionsExtrato") {
                                params.Page = scope[gridName].paginationCurrentPage;
                                params.Take = scope[gridName].paginationPageSize;
                            } else {
                                params.Take = 50000;
                            }
                           
                            

                            if (angular.isUndefined(params.Filters) || params.Filters == null)
                                params.Filters = [];

                            // Caso haja 
                            if (angular.isObject(filtrosPorColuna) && filtrosPorColuna.length > 0)
                                if (angular.isArray(filtrosPorColuna))
                                    filtrosPorColuna.forEach(function (xxyy) {
                                        params.Filters.push(xxyy);
                                    });

                            // Roda o queryFiltersOptionalFn..
                            if (queryFiltersOptionalFn.length > 0)
                                queryFiltersOptionalFn.forEach(function (ref) {
                                    params.Filters.push(ref);
                                });

                            params.filtrosTela = scope[gridName].filtrosTela;
                            params.extensao = scope[gridName].extensao;
                            params.isRelatorio = 1;

                            $http.post(URL_SERVER_DEV + scope[gridName].urlRelatorio, params, access).then(function (response) {
                                if (response.data.success) {
                                    callBack(response);
                                }
                            });
                        }
                        
                        if (angular.isFunction(callback)) {
                            callback();
                        }
                    };
                },
                defaultOnRegisterApi: function (gridApi) {
                    var scope = gridApi.grid.appScope.vm;
                    scope.gridApi = gridApi;

                    // Caso a grid tenha lastState, então chamamos o 'restore'
                    var lastState = scope.gridApi.lastState;
                    if (angular.isDefined(lastState))
                        scope.gridApi.saveState.restore(scope, lastState);

                    // Tratamento de DEFAULTS
                    // Grid menu
                    if (angular.isUndefined(scope.gridOptions.enableGridMenu))
                        scope.gridOptions.enableGridMenu = true;

                    if (angular.isUndefined(scope.gridOptions.data))
                        scope.gridOptions.data = [];

                    scope.gridOptions.getGridHeight = function () {
                        var minumoRows = 10;
                        var rowsLen = scope.gridOptions.data.length;
                        return ((((rowsLen < minumoRows ? minumoRows : rowsLen)) * 30) + 150 + 'px');
                    };


                    // Filtro na grid
                    // Forçando para true
                    scope['gridOptions']['enableFiltering'] = true;
                    scope['gridOptions']['useExternalSorting'] = true;
                    scope['gridOptions']['useExternalFiltering'] = true;
                    scope['gridOptions']['enableSorting'] = true;
                    scope['gridOptions']['enableRowHashing'] = true;

                    if (angular.isUndefined(scope.gridOptions.paginationPageSizes) ||
                        scope.gridOptions.paginationPageSizes.toString() === [250, 500, 1000].toString())
                        scope.gridOptions.paginationPageSizes = [10, 25, 50, 100, 250, 500];

                    // Items por página
                    if (angular.isUndefined(scope.gridOptions.paginationPageSize) ||
                        scope.gridOptions.paginationPageSize === 250)
                        scope.gridOptions.paginationPageSize = 10;

                    // Página atual
                    if (angular.isUndefined(scope.gridOptions.paginationCurrentPage))
                        scope.gridOptions.paginationCurrentPage = 1;

                    // Força sempre usar paginação externa
                    scope.gridOptions.useExternalPagination = true;


                    scope.gridOptions.columnDefs.forEach(function (item) {
                        // Aggregates menu
                        if (!item.groupingShowAggregationMenu)
                            item.groupingShowAggregationMenu = false;
                        // Defaults para PK
                        if (item.primaryKey && angular.isUndefined(item.serverField)) {
                            item.enableFiltering = true;
                            item.enableGrouping = false;
                            if (angular.isUndefined(item.width))
                                item.width = "5%";
                        }

                        if (item.type === "number" || item.primaryKey)
                            item.cellClass = "rAlign";

                        if (angular.isUndefined(item.field) || item.name === 'Ações') {
                            item.enableGrouping = false;
                            item.enableSorting = false;
                            item.enableFiltering = false;
                            item.enableColumnMenu = false;

                            if (item.name === 'Ações') {
                                item.cellTemplate = "<center>" + item.cellTemplate + "</center>";
                                // item.pinnedRight = true;
                                // item.pinned = true;
                                // item.width = "*";
                            }
                        }

                        // largura...
                        if (angular.isUndefined(item.width)) item.width = "15%";

                        if (angular.isUndefined(item.headerCellClass))
                            item.headerCellClass = highlightFilteredHeader;

                        if (angular.isUndefined(item.enableFiltering) || item.enableFiltering === null &&
                            (angular.isDefined(item.field) || angular.isDefined(item.serverField)))
                            item.enableFiltering = true;

                        if (item.sel) {
                            item.enableFiltering = false;
                            item.enableGrouping = false;
                            item.enableSorting = false;
                            item.cellTemplate = fn.dataGrid.getSelTemplate();
                        }

                        scope.gridApi.grid.getCurrentModel = function () {
                            //create a ramdom name...
                            return 'grid.appScope.vm.select' + (Math.random(0, 100000000) * 100).toString().split('.')[0];
                        }

                        if (item.enum && item.enableFiltering) {
                            if (angular.isUndefined(item.filters))
                                item.filters = [{
                                    type: uiGridConstants.filter.SELECT
                                }];

                            if (angular.isDefined(item.enumTipo)) {
                                if (item.enumTipo === 'ESimNao') {
                                    var enumSimNao = DefaultsService.enums.ESimNao.get();
                                    item.filters[0].selectOptions = enumSimNao;
                                } else if (item.enumTipo === 'EMensagensTratadasNaoTratadas') {
                                    item.filters[0].selectOptions = DefaultsService.enums.EMensagensTratadasNaoTratadas.get();
                                } else if (item.enumTipo === 'EAprovadoReprovado') {
                                    item.filters[0].selectOptions = DefaultsService.enums.EAprovadoReprovado.get();
                                } else if (item.enumTipo === 'EStatusProtocoloEvento_SemRejeiado') {
                                    var enumProtocolo = DefaultsService.enums.EStatusProtocoloEvento_SemRejeiado.get();
                                    item.filters[0].selectOptions = enumProtocolo;
                                } else if (item.enumTipo === 'EStatusOcorrencia') {
                                    var enumOcorrencia = DefaultsService.enums.EStatusOcorrencia.get();
                                    item.filters[0].selectOptions = enumOcorrencia;
                                } else if (item.enumTipo === 'EStatusChecklistAgendamento') {
                                    var enumOcorrencia = DefaultsService.enums.EStatusChecklistAgendamento.get();
                                    item.filters[0].selectOptions = enumOcorrencia;
                                } else if (item.enumTipo === 'EStatusCadastros') {
                                    var enumStatusCadastros = DefaultsService.enums.EStatusCadastros.get();
                                    item.filters[0].selectOptions = enumStatusCadastros;
                                } else if (item.enumTipo === 'ETipoPagamentos') {
                                    var enumTipoPagamentos = DefaultsService.enums.ETipoPagamentos.get();
                                    item.filters[0].selectOptions = enumTipoPagamentos;
                                } else if (item.enumTipo === 'EProcessoVinculados') {
                                    var enumProcessoVinculados = DefaultsService.enums.EProcessoVinculados.get();
                                    item.filters[0].selectOptions = enumProcessoVinculados;
                                } else if (item.enumTipo === 'EFormaPagamentos') {
                                    var enumFormaPagamentos = DefaultsService.enums.EFormaPagamentos.get();
                                    item.filters[0].selectOptions = enumFormaPagamentos;
                                } else if (item.enumTipo === 'ETipoEmissaoCIOT') {
                                    var enumTipoEmissaoCIOT = DefaultsService.enums.ETipoEmissaoCIOT.get();
                                    item.filters[0].selectOptions = enumTipoEmissaoCIOT;
                                } else if (item.enumTipo === 'ECodigoAplicacao') {
                                    var enumCodigoAplicacao = DefaultsService.enums.ECodigoAplicacao.get();
                                    item.filters[0].selectOptions = enumCodigoAplicacao;
                                } else if (item.enumTipo === 'ETipoMensagem') {
                                    var enumTipoMensagem = DefaultsService.enums.ETipoMensagem.get();
                                    item.filters[0].selectOptions = enumTipoMensagem;
                                } else if (item.enumTipo === 'ETipoCIOT') {
                                    var enumTipoCiot = DefaultsService.enums.ETipoCIOT.get();
                                    item.filters[0].selectOptions = enumTipoCiot;
                                } else if (item.enumTipo === 'ETipoEvento') {
                                    var enumTipoCiot = DefaultsService.enums.ETipoEvento.get();
                                    item.filters[0].selectOptions = enumTipoCiot;
                                } else if (item.enumTipo === 'EFormaPagamentoEvento') {
                                    var enumFormaPagamentoEvento = DefaultsService.enums.EFormaPagamentoEvento.get();
                                    item.filters[0].selectOptions = enumFormaPagamentoEvento;
                                } else if (item.enumTipo === 'EStatusTransacao') {
                                    var enumTipoCiot = DefaultsService.enums.EStatusTransacao.get();
                                    item.filters[0].selectOptions = enumTipoCiot;
                                } else if (item.enumTipo === 'EStatusCredenciamentoPosto') {
                                    var enumTipoCiot = DefaultsService.enums.EStatusCredenciamentoPosto.get();
                                    item.filters[0].selectOptions = enumTipoCiot;
                                }else if (item.enumTipo === 'EFarolSla') {
                                    var enumTipoCiot = DefaultsService.enums.EFarolSla.get();
                                    item.filters[0].selectOptions = enumTipoCiot;
                                }else if (item.enumTipo === 'EMetodoAbastecimento') {
                                    var enumTipoCiot = DefaultsService.enums.EMetodoAbastecimento.get();
                                    item.filters[0].selectOptions = enumTipoCiot;
                                } else if (item.enumTipo === 'EStatusNotificacoes') {
                                    var enumTipoCiot = DefaultsService.enums.EStatusNotificacoes.get();
                                    item.filters[0].selectOptions = enumTipoCiot;
                                } else if (item.enumTipo === 'EStatusCIOT') {
                                    var enumStatusCiot = DefaultsService.enums.EStatusCIOT.get();
                                    item.filters[0].selectOptions = enumStatusCiot;
                                } else if (item.enumTipo === 'EStatusTipoOperacao') {
                                    var enumStatusTipoOperacao = DefaultsService.enums.EStatusTipoOperacao.get();
                                    item.filters[0].selectOptions = enumStatusTipoOperacao;
                                } else if (item.enumTipo === 'EStatusAutorizacaoAbastecimento') {
                                    var enumStatusAutorizacaoAbastecimento = DefaultsService.enums.EStatusAutorizacaoAbastecimento.get();
                                    item.filters[0].selectOptions = enumStatusAutorizacaoAbastecimento;
                                } else if (item.enumTipo === 'EStatusPagamentos') {
                                    var enumStatusPagamentos = DefaultsService.enums.EStatusPagamentos.get();
                                    item.filters[0].selectOptions = enumStatusPagamentos;
                                } else if (item.enumTipo === 'EStatusViagem') {
                                    var enumStatusViagem = DefaultsService.enums.EStatusViagem.get();
                                    item.filters[0].selectOptions = enumStatusViagem;
                                } else if (item.enumTipo === 'EStatusPagamentoPedagio') {
                                    var enumStatusPagamentoPedagio = DefaultsService.enums.EStatusPagamentoPedagio.get();
                                    item.filters[0].selectOptions = enumStatusPagamentoPedagio;
                                } else if (item.enumTipo === 'EStatusPagamentoPedagioConsultaCentralPendencias') {
                                    var enumStatusPagamentoPedagioConsultaCentralPendencias = DefaultsService.enums.EStatusPagamentoPedagioConsultaCentralPendencias.get();
                                    item.filters[0].selectOptions = enumStatusPagamentoPedagioConsultaCentralPendencias;
                                } else if (item.enumTipo === 'EFormaPagamentoPedagio') {
                                    var enumFormaPagamentoPedagio = DefaultsService.enums.EFormaPagamentoPedagio.get();
                                    item.filters[0].selectOptions = enumFormaPagamentoPedagio;
                                } else if (item.enumTipo === 'EStatusServidorCiot') {
                                    var enumStatusServidorCiot = DefaultsService.enums.EStatusServidorCiot.get();
                                    item.filters[0].selectOptions = enumStatusServidorCiot;
                                } else if (item.enumTipo === 'EStatusComunicacaoAntt') {
                                    var enumStatusComunicacaoAntt = DefaultsService.enums.EStatusComunicacaoAntt.get();
                                    item.filters[0].selectOptions = enumStatusComunicacaoAntt;
                                } else if (item.enumTipo === 'ETipoServidor') {
                                    var enumTipoServidor = DefaultsService.enums.ETipoServidor.get();
                                    item.filters[0].selectOptions = enumTipoServidor;
                                } else if (item.enumTipo === 'EStatusPagamentoEvento') {
                                    var enumStatusPagamentoEvento = DefaultsService.enums.EStatusPagamentoEvento.get();
                                    item.filters[0].selectOptions = enumStatusPagamentoEvento;
                                } else if (item.enumTipo === 'ETipoContaDock') {
                                    var enumTipoContaDock = DefaultsService.enums.ETipoContaDock.get();
                                    item.filters[0].selectOptions = enumTipoContaDock;
                                } else if (item.enumTipo === 'EContingenciaCiot') {
                                    var enumContigenciaCiot = DefaultsService.enums.EContingenciaCiot.get();
                                    item.filters[0].selectOptions = enumContigenciaCiot;
                                } else if (item.enumTipo === 'ECiotEncerrado') {
                                    var enumCiotEncerrado = DefaultsService.enums.ECiotEncerrado.get();
                                    item.filters[0].selectOptions = enumCiotEncerrado;
                                } else if (item.enumTipo === 'EVersaoIntegracao') {
                                    var enumVersaIntegracaoPagamento = DefaultsService.enums.EVersaoIntegracao.get();
                                    item.filters[0].selectOptions = enumVersaIntegracaoPagamento;
                                } else if (item.enumTipo === 'EStatusPortador') {
                                    var enumStatusPortador = DefaultsService.enums.EStatusPortador.get();
                                    item.filters[0].selectOptions = enumStatusPortador;
                                } else
                                    DefaultsService.enums.get(item.enumTipo).then(function (retorno) {
                                        item.filters[0].selectOptions = retorno.data;
                                    });
                            } else {
                                if (item.enumData)
                                    item.filters[0].selectOptions = item.enumData;
                                else {
                                    if (item.enumOptions) {
                                        if (!item.enumOptions.url)
                                            $log.warn("ATS Grid - Error: Não é possível carregar uma select sem url[" + item.field + "]");
                                        else if (!item.enumOptions.type)
                                            $log.warn("ATS Grid - Error: Não é possível carregar uma select com uma URL sem type[" + item.field + "]");
                                        else if (!item.enumOptions.responseDataField)
                                            $log.warn("ATS Grid - Error: Não é possível carregar uma select sem responseDataField[" + item.field + "]");
                                        else
                                            $http[item.enumOptions.type.toLowerCase()]
                                            (URL_SERVER_DEV + item.enumOptions.url, item.enumOptions.params, access)
                                                .then(function (response) {
                                                    var items = response.data.data[item.enumOptions.responseDataField];
                                                    if (angular.isDefined(items))
                                                        items.forEach(function (i) {
                                                            if (i.label.length > 35) i.label = i.label.slice(0, 35) + "...";
                                                        });

                                                    item.filters[0].selectOptions = items;
                                                });
                                    } else
                                        $log.warn('GridOverFlow: Não foi possível carregar dados para o filtro ' + item.field);
                                }
                            }
                            if (angular.isDefined(item.selectedFilter)) {
                                item.filters[0].term = item.filters[0].selectOptions[item.selectedFilter].value;
                            }
                        }

                        if (item.type === 'date' && item.enableFiltering) {
                            item.filterHeaderTemplate = 'customDateFieldTemplateUiGrid',
                                item.filters = [{
                                    condition: uiGridConstants.filter.GREATER_THAN_OR_EQUAL,
                                    placeholder: 'Maior igual á',
                                    type: 'date'
                                }, {
                                    condition: uiGridConstants.filter.LESS_THAN_OR_EQUAL,
                                    placeholder: 'Menor igual que',
                                    type: 'date'
                                }];
                        }

                        // Coloca agrupamento em todas colunas que tem serverField e 
                        // o enableGrouping seja indefinido
                        if (angular.isDefined(item.serverField) && angular.isUndefined(item.enableGrouping) && !item.primaryKey)
                            item.enableGrouping = true;

                        if (angular.isUndefined(item.enableGrouping))
                            item.enableGrouping = false;

                        if (item.name === 'Ações')
                            item.minWidth = 70;
                    });
                    // Fim 
                    if (angular.isDefined(scope.gridApi.pagination))
                        scope.gridApi.pagination.on.paginationChanged(null, function (newPage, pageSize) {
                            loadData(newPage, pageSize);
                            $timeout(function () {
                                $window.dispatchEvent(new Event('resize'));
                            }, 500);
                        });

                    gridApi.grid.options.onGridPageSizeChange = function (pageSize) {

                    };

                    var filtrosPorColuna = [];
                    scope.gridApi.grid.options.filtrosPorColuna = [];
                    scope.gridApi.core.on.filterChanged(null, function () {
                        realizarFiltro(this.grid);
                        $timeout(function () {
                            $window.dispatchEvent(new Event('resize'));
                        }, 500);
                    });

                    function realizarFiltro(grid) {
                        // IsEqual = 0,
                        // Contains = 1,
                        // HigherThan = 2,
                        // LowerThan = 3
                        // LessThanOrEqual = 256

                        // var grid = grid;

                        filtrosPorColuna = [];

                        grid.columns.forEach(function (item) {
                            item.filters.forEach(function (filtro) {
                                if (angular.isDefined(filtro) && angular.isDefined(filtro.term) && filtro.term !== null)
                                    if ((item.colDef.serverField || item.colDef.field) && filtro.term !== null && filtro.term !== "") {
                                        filtrosPorColuna.push({
                                            Campo: item.colDef.serverField || item.colDef.field,
                                            Operador: filtro.condition || uiGridConstants.filter.CONTAINS,
                                            Valor: filtro.term,
                                            ServerFieldCollection: item.colDef.serverFieldCollection,
                                            CampoTipo: item.colDef.type === 'date' ? 0 :
                                                (item.colDef.type === 'number' || item.colDef.enum ? 2 :
                                                    (item.colDef.type === 'interval' ? 3 : 1))
                                        });
                                        scope.gridApi.grid.options.filtrosPorColuna.push({
                                            Campo: item.colDef.serverField || item.colDef.field,
                                            Operador: filtro.condition || uiGridConstants.filter.CONTAINS,
                                            Valor: filtro.term,
                                            ServerFieldCollection: item.colDef.serverFieldCollection,
                                            CampoTipo: item.colDef.type === 'date' ? 0 : (item.colDef.type === 'number' || item.colDef.enum ? 2 : 1)
                                        });
                                    }
                            })
                        });

                        loadData(gridApi.grid.options.paginationCurrentPage, gridApi.grid.options.paginationPageSize);
                    }

                    if (gridApi.grid.options.dataSource &&
                        (gridApi.grid.options.dataSource.autoBind || angular.isUndefined(gridApi.grid.options.dataSource.autoBind))) {
                        // Habilita paginação externa...
                        scope.gridOptions.useExternalPagination = true;
                        // Se já tiver algum dado, então não chamamos o dt, msm sendo autoBind true...
                        if (scope.gridOptions.data.length === 0) {
                            $timeout(function () {
                                realizarFiltro(gridApi.grid);
                            }, 250);
                        }
                    }

                    // When: Sorting start...
                    scope.gridApi.core.on.sortChanged(null, sortChanged);

                    var lastSortUsed;
                    gridApi.grid.options.lastSortUsed = undefined;

                    function sortChanged(grid, sortColumns) {
                        var coluna = sortColumns[sortColumns.length - 1];
                        if (sortColumns.length === 0 || angular.isUndefined(sortColumns)) {
                            lastSortUsed = undefined;
                            loadData(1, gridApi.grid.options.paginationPageSize);
                        } else {
                            lastSortUsed = {
                                Campo: (coluna.colDef.serverField ? coluna.colDef.serverField : coluna.field) || getFieldPrimaryKey(),
                                Operador: coluna.sort.direction === uiGridConstants.DESC ? 1 : 0
                            };
                            gridApi.grid.options.lastSortUsed = lastSortUsed;
                            loadData(gridApi.grid.options.paginationCurrentPage, gridApi.grid.options.paginationPageSize, lastSortUsed);
                        }
                    }

                    function getFieldPrimaryKey() {
                        var retorno = null;
                        gridApi.grid.options.columnDefs.map(function (x) {
                            if (x.primaryKey) retorno = x.field;
                        });

                        return retorno;
                    }

                    function getFieldPrimaryKeyOrientation() {
                        var retorno = null;
                        gridApi.grid.options.columnDefs.map(function (x) {
                            if (x.primaryKey) retorno = x.orientation;
                        });

                        return retorno;
                    }

                    function highlightFilteredHeader(row, rowRenderIndex, col) {
                        if (col.filters[0].term)
                            return 'header-filtered';
                        else return '';
                    }

                    function loadData(newPage, pageSize, sortCfg) {
                        $timeout(function () {
                            $window.dispatchEvent(new Event('resize'));
                        }, 500);

                        scope.gridOptions.data.length = 0;

                        scope.gridOptions.virtualizationThreshold = pageSize;
                        scope.gridOptions.loading = true;

                        var dtSrc = scope.gridApi.grid.options.dataSource;
                        var params = angular.isFunction(dtSrc.params) ? dtSrc.params() : {};
                        var queryFiltersOptionalFnRet = angular.isFunction(dtSrc.queryFiltersOptionalFn) ? dtSrc.queryFiltersOptionalFn() : [];


                        params.Page = newPage || 1;
                        params.Take = pageSize || gridApi.grid.options.paginationPageSize;

                        if (sortCfg || lastSortUsed)
                            params.Order = {
                                Campo: sortCfg ? sortCfg.Campo : lastSortUsed.Campo,
                                Operador: sortCfg ? sortCfg.Operador : lastSortUsed.Operador
                            };
                        else {
                            var pk = getFieldPrimaryKey();
                            if (!pk || angular.isUndefined(pk) || pk === null)
                                $log.info('Não foi possível encontrar uma primary key para esta tabela, não haverá ordenamento padrão');
                            else {
                                var orientation = getFieldPrimaryKeyOrientation();

                                params.Order = {
                                    Campo: pk,
                                    Operador: angular.isDefined(orientation) ? orientation : 1
                                };
                            }
                        }
                        if (angular.isUndefined(params.Filters) || params.Filters == null)
                            params.Filters = [];

                        // Caso haja 
                        if (angular.isObject(filtrosPorColuna) && filtrosPorColuna.length > 0)
                            if (angular.isArray(filtrosPorColuna))
                                filtrosPorColuna.forEach(function (xxyy) {
                                    params.Filters.push(xxyy);
                                });

                        // Roda o queryFiltersOptionalFnRet..
                        if (queryFiltersOptionalFnRet.length > 0)
                            queryFiltersOptionalFnRet.forEach(function (ref) {
                                params.Filters.push(ref);
                            });

                        // Busca os dados no servidor. sim... é POST e não GET, GET não aceito objetos na requisição
                        $http.post(URL_SERVER_DEV + scope.gridApi.grid.options.dataSource.url, params, access).then(function (response) {

                            ;
                            if (response.data && response.data.data && response.data.data.items &&
                                response.data.data.items.length > 0) {

                                scope.gridOptions.data = response.data.data.items;
                                scope.gridOptions.totalItems = response.data.data.totalItems;
                                scope.gridOptions.totalizador = response.data.data.totalizador;
                                scope.gridOptions.totalPgtos = response.data.data.totalPgtos;
                                for (var i = 0; i < scope.gridOptions.columnDefs.length; i++) {
                                    if (angular.isDefined(scope.gridOptions.columnDefs[i].campoTotalizador)) {
                                        scope.gridOptions.totalizador[scope.gridOptions.columnDefs[i].campoTotalizador] = response.data.data[scope.gridOptions.columnDefs[i].campoTotalizador];
                                    }
                                }

                                if (angular.isFunction(scope.gridApi.grid.options.callBack))
                                    scope.gridApi.grid.options.callBack(response.data);
                            } else {
                                scope.gridOptions.data = [];
                                scope.gridOptions.totalItems = 0;
                            }

                            if (!response.data.success) {
                                if (response.data.message !== null &&
                                    response.data.message !== '' &&
                                    !angular.isUndefined(response.data.message)) {
                                    toastr.error(response.data.message);
                                }
                            }
                            if (dtSrc.events) {
                                if (angular.isFunction(dtSrc.events.onDataBound)) {
                                    dtSrc.events.onDataBound(scope.gridOptions.data);
                                }
                            }

                        }).finally(function () {
                            // Remove o gif de loading...
                            scope.gridOptions.loading = false;
                            // Salva o estado da grid...
                            scope.gridOptions.lastState = scope.gridApi.saveState.save();

                            $timeout(function () {
                                $window.dispatchEvent(new Event('resize'));
                            }, 500);
                        });
                    }

                    function getDataRelatorio(newPage, pageSize, sortCfg, callback) {
                        $timeout(function () {
                            $window.dispatchEvent(new Event('resize'));
                        }, 600);

                        var dtSrc = scope.gridApi.grid.options.dataSource;
                        var params = angular.isFunction(dtSrc.params) ? dtSrc.params() : {};
                        var queryFiltersOptionalFnRet = angular.isFunction(dtSrc.queryFiltersOptionalFn) ? dtSrc.queryFiltersOptionalFn() : [];

                        params.Page = newPage || 1;
                        params.Take = pageSize || gridApi.grid.options.paginationPageSize;

                        if (sortCfg || lastSortUsed)
                            params.Order = {
                                Campo: sortCfg ? sortCfg.Campo : lastSortUsed.Campo,
                                Operador: sortCfg ? sortCfg.Operador : lastSortUsed.Operador
                            };
                        else {
                            var pk = getFieldPrimaryKey();
                            if (!pk || angular.isUndefined(pk) || pk === null)
                                $log.info('Não foi possível encontrar uma primary key para esta tabela, não haverá ordenamento padrão');
                            else
                                params.Order = {
                                    Campo: pk,
                                    Operador: 1
                                };
                        }

                        if (angular.isUndefined(params.Filters) || params.Filters == null)
                            params.Filters = [];

                        // Caso haja 
                        if (angular.isObject(filtrosPorColuna) && filtrosPorColuna.length > 0)
                            if (angular.isArray(filtrosPorColuna))
                                filtrosPorColuna.forEach(function (xxyy) {
                                    params.Filters.push(xxyy);
                                });

                        // Roda o queryFiltersOptionalFnRet..
                        if (queryFiltersOptionalFnRet.length > 0)
                            queryFiltersOptionalFnRet.forEach(function (ref) {
                                params.Filters.push(ref);
                            });

                        if (angular.isUndefined(scope.gridOptions.urlRelatorio))
                            return;

                        if (angular.isUndefined(scope.gridOptions.extensao))
                            return;

                        if (angular.isUndefined(scope.gridOptions.filtrosTela))
                            return;

                        if (angular.isUndefined(scope.gridOptions.nomeRelatorio))
                            return;

                        params.filtrosTela = scope.gridOptions.filtrosTela;
                        params.extensao = scope.gridOptions.extensao;
                        // Busca os dados no servidor. sim... é POST e não GET, GET não aceito objetos na requisição
                        $http.post(URL_SERVER_DEV + scope.gridOptions.urlRelatorio, params, access).then(function (response) {
                            if (response.data.success) {
                                var data = response.data.data;
                                var typeReport = undefined;

                                if (scope.gridOptions.extensao == "pdf")
                                    typeReport = "application/pdf";

                                if (scope.gridOptions.extensao == "xls")
                                    typeReport = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

                                var bytes = new Uint8Array(data.length);

                                for (var i = 0; i < data.length; i++) {
                                    bytes[i] = data[i];
                                }

                                var blob = new Blob([bytes], {
                                    type: typeReport
                                })
                                saveAs(blob, scope.gridOptions.nomeRelatorio + "." + scope.gridOptions.extensao)

                                if (angular.isFunction(callback))
                                    callback();
                            }
                        });
                    }

                    // Método para recarregar a grid..
                    // if (angular.isDefined(scope.gridApi.grid.options.dataSource))
                    //     scope.gridApi.grid.options.dataSource.refresh = function () {
                    //         var scope = gridApi.grid.appScope.vm;
                    //         if (scope.gridOptions.paginationCurrentPage === 1)
                    //             loadData(1, gridApi.grid.options.paginationPageSize, null);
                    //         else
                    //             scope.gridApi.pagination.seek(1);
                    //     };

                    function getDataRelatorio(newPage, pageSize, sortCfg, callback) {
                        $timeout(function () {
                            $window.dispatchEvent(new Event('resize'));
                        }, 500);

                        var dtSrc = scope.gridApi.grid.options.dataSource;
                        var params = angular.isFunction(dtSrc.params) ? dtSrc.params() : {};
                        var queryFiltersOptionalFnRet = angular.isFunction(dtSrc.queryFiltersOptionalFn) ? dtSrc.queryFiltersOptionalFn() : [];

                        params.Page = newPage || 1;
                        params.Take = pageSize || gridApi.grid.options.paginationPageSize;

                        if (sortCfg || lastSortUsed)
                            params.Order = {
                                Campo: sortCfg ? sortCfg.Campo : lastSortUsed.Campo,
                                Operador: sortCfg ? sortCfg.Operador : lastSortUsed.Operador
                            };
                        else {
                            var pk = getFieldPrimaryKey();
                            if (!pk || angular.isUndefined(pk) || pk === null)
                                $log.info('Não foi possível encontrar uma primary key para esta tabela, não haverá ordenamento padrão');
                            else
                                params.Order = {
                                    Campo: pk,
                                    Operador: 1
                                };
                        }

                        if (angular.isUndefined(params.Filters) || params.Filters == null)
                            params.Filters = [];

                        // Caso haja 
                        if (angular.isObject(filtrosPorColuna) && filtrosPorColuna.length > 0)
                            if (angular.isArray(filtrosPorColuna))
                                filtrosPorColuna.forEach(function (xxyy) {
                                    params.Filters.push(xxyy);
                                });

                        // Roda o queryFiltersOptionalFnRet..
                        if (queryFiltersOptionalFnRet.length > 0)
                            queryFiltersOptionalFnRet.forEach(function (ref) {
                                params.Filters.push(ref);
                            });

                        if (angular.isUndefined(scope.gridOptions.urlRelatorio))
                            return;

                        if (angular.isUndefined(scope.gridOptions.extensao))
                            return;

                        var parametrosExtras = scope.gridOptions.parametrosExtras;
                        params.extensao = scope.gridOptions.extensao;

                        if (_.some(parametrosExtras))
                            _.assign(params, parametrosExtras);

                        var data = JSON.stringify(params);
                        var args = {
                            json: data
                        };
                        var url = URL_SERVER_DEV + scope.gridOptions.urlRelatorio;

                        openWindowWithPost(url, args);
                    }

                    // Método para recarregar a grid..
                    if (angular.isDefined(scope.gridApi.grid.options.dataSource))
                        scope.gridApi.grid.options.dataSource.refresh = function () {
                            var scope = gridApi.grid.appScope.vm;
                            if (scope.gridOptions.paginationCurrentPage === 1)
                                loadData(1, gridApi.grid.options.paginationPageSize, null);
                            else
                                scope.gridApi.pagination.seek(1);
                        };

                    // Método para recarregar a grid..
                    if (angular.isDefined(scope.gridApi.grid.options.dataSource))
                        scope.gridApi.grid.options.dataSource.gerarRelatorio = function (callback) {
                            var scope = gridApi.grid.appScope.vm;
                            getDataRelatorio(1, gridApi.grid.options.paginationPageSize, null, callback);
                        };

                    // Método para recarregar a grid..
                    scope.gridApi.grid.options.dataSource.gerarRelatorio = function (callback) {
                        var scope = gridApi.grid.appScope.vm;
                        getDataRelatorio(1, gridApi.grid.options.paginationPageSize, null, callback);
                    };
                    
                    scope.gridApi.grid.options.dataSource.consultarDadosRelatorio = function (callBack) {
                        $timeout(function () {
                            $window.dispatchEvent(new Event('resize'));
                        }, 600);

                        var dtSrc = scope.gridApi.grid.options.dataSource;
                        var params = angular.isFunction(dtSrc.params) ? dtSrc.params() : {};
                        var queryFiltersOptionalFnRet = angular.isFunction(dtSrc.queryFiltersOptionalFn) ? dtSrc.queryFiltersOptionalFn() : [];

                        var newPage = 1;
                        var sortCfg = null;

                        params.Page = newPage || 1;
                        params.Take = 50000;

                        if (sortCfg || lastSortUsed)
                            params.Order = {
                                Campo: sortCfg ? sortCfg.Campo : lastSortUsed.Campo,
                                Operador: sortCfg ? sortCfg.Operador : lastSortUsed.Operador
                            };
                        else {
                            var pk = getFieldPrimaryKey();
                            if (!pk || angular.isUndefined(pk) || pk === null)
                                $log.info('Não foi possível encontrar uma primary key para esta tabela, não haverá ordenamento padrão');
                            else
                                params.Order = {
                                    Campo: pk,
                                    Operador: 1
                                };
                        }

                        if (angular.isUndefined(params.Filters) || params.Filters == null)
                            params.Filters = [];

                        // Caso haja 
                        if (angular.isObject(filtrosPorColuna) && filtrosPorColuna.length > 0)
                            if (angular.isArray(filtrosPorColuna))
                                filtrosPorColuna.forEach(function (xxyy) {
                                    params.Filters.push(xxyy);
                                });

                        // Roda o queryFiltersOptionalFnRet..
                        if (queryFiltersOptionalFnRet.length > 0)
                            queryFiltersOptionalFnRet.forEach(function (ref) {
                                params.Filters.push(ref);
                            });

                        if (angular.isUndefined(scope.gridOptions.urlRelatorio))
                            return;

                        params.filtrosTela = scope.gridOptions.filtrosTela;
                        params.extensao = scope.gridOptions.extensao;
                        params.isRelatorio = 1;
                        // Busca os dados no servidor. sim... é POST e não GET, GET não aceito objetos na requisição
                        $http.post(URL_SERVER_DEV + scope.gridOptions.urlRelatorio, params, access).then(function (response) {
                            if (response.data.success) {
                                callBack(response);
                            }
                        });
                    }
                },
                dataSource: function (url, paramsFn, queryFiltersOptional) {
                    if (angular.isUndefined(url) || url === null || url.length === 0)
                        throw new Error('GridOverFlow: Impossível inicializar a grid sem URL!');

                    var retorno = {
                        url: url
                    };

                    if (angular.isFunction(paramsFn))
                        retorno.params = paramsFn;

                    // Sempre deve retornar um array... msm que seja vazio...
                    if (angular.isFunction(queryFiltersOptional))
                        retorno.queryFiltersOptionalFn = queryFiltersOptional;

                    return retorno;
                },
                getSelTemplate: function () {
                    return '<input type="checkbox" ng-model="row.entity.sel" ng-change="grid.appScope.vm.selEvent(row.entity)" value=""/>';
                }
            },
            exportarTabelaEmExcel: function (idElementoHtml, nomeArquivo, formatoXLS) {

                var table = document.getElementById(idElementoHtml);

                // Variable to store the final csv data
                var csv_data = [];
                var exportType;
                var exportExtension;

                // Get each row data
                var rows = table.getElementsByTagName('tr');
                for (var i = 0; i < rows.length; i++) {

                    // Get each column data
                    var cols = rows[i].querySelectorAll('td,th');

                    // Stores each csv row data
                    var csvrow = [];
                    for (var j = 0; j < cols.length; j++) {

                        // get the text data of each cell of
                        // a row and push it to csvrow
                        csvrow.push(cols[j].innerHTML.trim());
                    }

                    // combine each column value with tab
                    csv_data.push(csvrow.join('\t'));
                }
                // combine each row data with new line character
                csv_data = csv_data.join('\r\n');

                if (formatoXLS) {
                    exportType = "application/vnd.ms-excel"
                    exportExtension = ".xls"
                } else {
                    exportType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    exportExtension = ".xlsx"
                }

                nomeArquivo = nomeArquivo + exportExtension;

                var downloadLink = document.createElement("a");

                document.body.appendChild(downloadLink);

                if(navigator.msSaveOrOpenBlob){
                    var blob = new Blob(['\ufeff', csv_data], {
                        type: exportType
                    });
                    navigator.msSaveOrOpenBlob( blob, nomeArquivo);
                }else{
                    // Create a link to the file
                    downloadLink.href = 'data:' + exportType + ', ' + csv_data;

                    // Setting the file name
                    downloadLink.download = nomeArquivo;

                    //triggering the function
                    downloadLink.click();
                }
            },
            exportarTabelaEmExcel2: function (idElementoHtml, nomeArquivo) {
                var table = document.getElementById(idElementoHtml);
                var rows = table.querySelectorAll('tr');

                // Cria uma matriz para armazenar os dados
                var data = [];

                // Loop pelas linhas da tabela
                for (var i = 0; i < rows.length; i++) {
                    var row = rows[i];
                    var rowData = [];

                    // Loop pelas colunas da linha atual
                    var cols = row.querySelectorAll('td, th');
                    for (var j = 0; j < cols.length; j++) {
                        rowData.push(cols[j].innerText.trim());
                    }

                    data.push(rowData);
                }

                var exportType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                
                var workbook = XLSX.utils.book_new();
                
                var worksheet = XLSX.utils.aoa_to_sheet(data);
                
                XLSX.utils.book_append_sheet(workbook, worksheet, "Planilha");
                
                var excelBlob = new Blob([s2ab(XLSX.write(workbook, { bookType: 'xlsx', type: 'binary' }))], { type: exportType });
                
                var url = window.URL.createObjectURL(excelBlob);
                var downloadLink = document.createElement("a");
                downloadLink.href = url;
                downloadLink.download = nomeArquivo;
                downloadLink.click();
                function s2ab(s) {
                    var buf = new ArrayBuffer(s.length);
                    var view = new Uint8Array(buf);
                    for (var i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
                    return buf;
                }
            },
            exportarTabelaEmTxt: function (idElementoHtml, nomeArquivo, exportarComoCsv) {
                var table = document.getElementById(idElementoHtml);

                // Variable to store the final csv data
                var csv_data = [];
                var exportType;
                var exportExtension;

                // Get each row data
                var rows = table.getElementsByTagName('tr');
                for (var i = 0; i < rows.length; i++) {

                    // Get each column data
                    var cols = rows[i].querySelectorAll('td,th');

                    // Stores each csv row data
                    var csvrow = [];
                    for (var j = 0; j < cols.length; j++) {

                        // get the text data of each cell of
                        // a row and push it to csvrow
                        csvrow.push(cols[j].innerHTML.trim());
                    }

                    // combine each column value with comma
                    csv_data.push(csvrow.join(";"));
                }
                // combine each row data with new line character
                csv_data = csv_data.join('\n');

                if (exportarComoCsv) {
                    exportType = "text/csv"
                    exportExtension = ".csv"
                } else {
                    exportType = "text/plain"
                    exportExtension = ".txt"
                }

                var blob = new Blob([csv_data], {
                    type: exportType
                });

                saveAs(blob, nomeArquivo + exportExtension);

            },
            exportarTabelaEmPdfAutoSize: function (idElementoHtml, titulo, nomeArquivo) {
                var doc = new jsPDF('l', 'mm', [1000, 1000]);

                var logoBase64 = "data:image/png;base64,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";
                
                var datetime = new Date().toLocaleString("pt-BR");

                var header = function () {
                    doc.setFontSize(10);
                    doc.setTextColor(40);
                    doc.setFontStyle('normal');
                    doc.addImage(logoBase64, 'PNG', 10, 5, 37.5, 12);
                    doc.text(titulo, 85, 14);
                    doc.setFontSize(9);
                    doc.text(datetime, 160, 14);
                };


                if (angular.isDefined(doc.autoTable)) {
                    doc.autoTable({
                        html: idElementoHtml,
                        theme: 'plain',
                        styles: {
                            fontSize: 8,
                            overflow: 'linebreak',
                            cellPadding: { right: 10 }  // Aplica o padding à direita para todas as colunas
                        },
                        margin: { top: 22 },
                        tableWidth: 'wrap',  // Faz a tabela "encaixar" no espaço disponível
                        didDrawPage: header
                    });
                }
                                

                doc.save(nomeArquivo + '.pdf');
            },
            exportarTabelaEmPdfNovo: function (idElementoHtml, titulo, nomeArquivo) {
                var doc = new jsPDF('p', 'mm', [1000, 1000]);

                var logoBase64 = "data:image/png;base64,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";
                
                var datetime = new Date().toLocaleString("pt-BR");

                var header = function () {
                    doc.setFontSize(10);
                    doc.setTextColor(40);
                    doc.setFontStyle('normal');
                    doc.addImage(logoBase64, 'PNG', 10, 5, 37.5, 12);
                    doc.text(titulo, 85, 14);
                    doc.setFontSize(6);
                    doc.text(datetime, 160, 14);
                };

                if (angular.isDefined(doc.autoTable))
                    doc.autoTable({
                        html: idElementoHtml,
                        theme: 'plain',
                        styles: {overflow: 'ellipsize', fontSize: 5},
                        margin: {top: 22},
                        didDrawPage: header
                    });

                doc.save(nomeArquivo + '.pdf');
            },
            exportarTabelaEmPdf: function (idElementoHtml, titulo, nomeArquivo) {
                var doc = new jsPDF('p', 'mm', [210, 297]);
                var header = function (data) {
                    doc.setFontSize(18);
                    doc.setTextColor(40);
                    doc.setFontStyle('bold');
                    doc.text(titulo, data.settings.margin.left, 10);
                };

                if (angular.isDefined(doc.autoTable))
                    doc.autoTable({
                        html: idElementoHtml,
                        theme: 'grid',
                        styles: {overflow: 'ellipsize', fontSize: 5},
                        margin: {top: 15},
                        beforePageContent: header
                    });

                doc.save(nomeArquivo + '.pdf');
            },
            openWindowWithPost: function (url, params) {
                var form = document.createElement("form");
                form.setAttribute("method", "post");
                form.setAttribute("action", url);
                form.setAttribute("target", "_blank");
                for (var i in params) {
                    if (params.hasOwnProperty(i)) {
                        var input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = i;
                        input.value = params[i];
                        form.appendChild(input);
                    }
                }
                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);
            },
            parseStringToFloat: function (value) {
                if (typeof (value) === "string") {
                    var quantidadePontos = value.split('.').length - 1;

                    for (var i = 0; i < quantidadePontos; i++)
                        value = value.replace('.', '');

                    value = value.replace(',', '.');
                    return parseFloat(value);
                } else {
                    return value;
                }
            }
        }

        return fn;

        function b64toBlob(b64Data, contentType, sliceSize) {
            contentType = contentType || '';
            sliceSize = sliceSize || 512;

            var byteCharacters = $window.atob(b64Data);
            var byteArrays = [];

            for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
                var slice = byteCharacters.slice(offset, offset + sliceSize);

                var byteNumbers = new Array(slice.length);
                for (var i = 0; i < slice.length; i++) {
                    byteNumbers[i] = slice.charCodeAt(i);
                }

                var byteArray = new Uint8Array(byteNumbers);

                byteArrays.push(byteArray);
            }

            var blob = new Blob(byteArrays, {
                type: contentType
            });
            return blob;
        }
    }

})();