<div id="CentralPendenciasDetalhesPagamentoController" ng-controller="CentralPendenciasDetalhesPagamentoController as vm">
    <form-header items="vm.headerItems" head="'Detalhes da Pendência'"></form-header>
    
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins" style="border-radius: 8px;">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>
                            Detalhes da Pendência #{{vm.pagamentoId}}
                          
                        </h5>

                          <span ng-if="!vm.carregandoPagamento && vm.pagamento.statusDescricao" class="label" ng-class="{
                                'label-success': vm.pagamento.status === 0 || vm.pagamento.statusDescricao === 'Fechado' || vm.pagamento.statusDescricao === 'Aprovado',
                                'label-warning': vm.pagamento.status === 1 || vm.pagamento.statusDescricao === 'Aberto',
                                'label-info': vm.pagamento.status === 2 || vm.pagamento.statusDescricao === 'Pendente',
                                'label-danger': vm.pagamento.status === 3 || vm.pagamento.statusDescricao === 'Erro',
                                'label-default': vm.pagamento.status === 4 || vm.pagamento.statusDescricao === 'Cancelado',
                                'label-primary': vm.pagamento.status === 5 || vm.pagamento.statusDescricao === 'Processando',
                                'label-muted': vm.pagamento.status === 6 || vm.pagamento.statusDescricao === 'Não Executado'
                            }" style="margin-left: 10px; font-size: 12px;">
                                {{vm.pagamento.statusDescricao}}
                            </span>
                        <div ibox-tools></div>
                    </div>

                    <div class="ibox-content" ng-show="vm.carregandoPagamento">
                        <div class="text-center">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                            <p class="text-muted">Carregando dados da pendência...</p>
                        </div>
                    </div>

                    <div class="ibox-content" ng-show="!vm.carregandoPagamento" style="background-color: #f8f9fa; padding: 30px;">

                        <div class="row">
                            <!-- Card: Informações da Pendência -->
                            <div class="col-md-8">
                                <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                                    <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                        <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center;">
                                            <i class="fa fa-info-circle" style="color: #1ab394; margin-right: 10px; font-size: 18px;"></i>
                                            Informações da Pendência
                                        </h5>
                                    </div>
                                    <div class="card-body" style="padding: 25px;">
                                        <div class="row">
                                        
                                             <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Código</label>
                                                    <div style="color: #495057; font-weight: 500;">{{vm.pagamento.id}}</div>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">ID da Viagem</label>
                                                    <div style="color: #495057; font-weight: 500;">{{vm.pagamento.viagemId}}</div>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Viagem Externa ID</label>
                                                    <div style="color: #495057; font-weight: 500;">{{vm.pagamento.viagemExternoId}}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                           

                                             <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">valor</label>
                                                    <div style="color: #495057; font-weight: 500;">{{vm.pagamento.valor}}</div>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Tipo</label>
                                                    <div style="color: #495057; font-weight: 500;">{{vm.pagamento.tipoDescricao}}</div>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Forma de Pagamento</label>
                                                    <div style="color: #495057; font-weight: 500;">{{vm.pagamento.formaPagamentoDescricao}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Card: Datas Principais -->
                            <div class="col-md-4">
                                <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                                    <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                        <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center;">
                                            <i class="fa fa-calendar" style="color: #f39c12; margin-right: 10px; font-size: 18px;"></i>
                                            Datas Principais
                                        </h5>
                                    </div>
                                    <div class="card-body" style="padding: 25px;">

                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Previsão Pagamento</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.dataPrevisaoPagamento}}</div>
                                        </div>

                            
                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Data Cadastro</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.dataCadastro}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Card: Informações do Proprietário -->
                            <div class="col-md-6">
                                <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                                    <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                        <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center;">
                                            <i class="fa fa-user" style="color: #3498db; margin-right: 10px; font-size: 18px;"></i>
                                            Informações do Proprietário
                                        </h5>
                                    </div>
                                    <div class="card-body" style="padding: 25px;">
                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Nome</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.nomeProprietario}}</div>
                                        </div>

                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">CPF/CNPJ</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.formatarCpfCnpj(vm.pagamento.cpfCnpjProprietario)}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Card: Informações da Empresa -->
                            <div class="col-md-6">
                                <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                                    <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                        <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center;">
                                            <i class="fa fa-building" style="color: #9b59b6; margin-right: 10px; font-size: 18px;"></i>
                                            Informações da Empresa
                                        </h5>
                                    </div>
                                    <div class="card-body" style="padding: 25px;">
                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Razão Social</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.razaoSocialEmpresa}}</div>
                                        </div>

                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">CNPJ</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.formatarCpfCnpj(vm.pagamento.cnpjEmpresa)}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Card: Informações de Antecipação -->
                        <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;" ng-if="vm.pagamento.antecipacaoMotivo || vm.pagamento.statusAntecipacaoDescricao">
                            <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center;">
                                    <i class="fa fa-fast-forward" style="color: #e67e22; margin-right: 10px; font-size: 18px;"></i>
                                    Informações de Antecipação
                                </h5>
                            </div>
                            <div class="card-body" style="padding: 25px;">
                                <div class="row">
                                    <div class="col-md-6" ng-if="vm.pagamento.statusAntecipacaoDescricao">
                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Status Antecipação</label>
                                            <span class="label label-success" style="font-size: 12px;">{{vm.pagamento.statusAntecipacaoDescricao}}</span>
                                        </div>
                                    </div>

                                    <div class="col-md-12" ng-if="vm.pagamento.antecipacaoMotivo">
                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Motivo da Antecipação</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.antecipacaoMotivo}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Card: Histórico de Datas 
                        <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                            <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center;">
                                    <i class="fa fa-calendar-o" style="color: #34495e; margin-right: 10px; font-size: 18px;"></i>
                                    Histórico de Datas
                                </h5>
                            </div>
                            <div class="card-body" style="padding: 25px;">
                                <div class="row">
                                    <div class="col-md-4" ng-if="vm.pagamento.dataBaixa">
                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Data Baixa</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.dataBaixa}}</div>
                                        </div>
                                    </div>

                                    <div class="col-md-4" ng-if="vm.pagamento.dataCadastroAntecipacao">
                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Data Cadastro Antecipação</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.dataCadastroAntecipacao}}</div>
                                        </div>
                                    </div>

                                    <div class="col-md-4" ng-if="vm.pagamento.dataAlteracaoAntecipacao">
                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Data Alteração Antecipação</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.dataAlteracaoAntecipacao}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>-->


                        <div class="row">
                            <div class="form-group">
                                <div class="col-md-12 col-lg-12 text-right">
                                    <button type="button" ng-disabled="vm.isSaving" ng-click="vm.voltar()" class="btn btn-labeled
                                        btn-default">
                                        <span class="btn-label">
                                            <i class="fa fa-arrow-circle-left"></i>
                                        </span>
                                        Voltar
                                    </button>
                                </div>
                            </div>
                        </div>


                        <!-- Card: Ações 
                        <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                            <div class="card-body" style="padding: 25px; text-align: center;">
                                <button type="button" class="btn btn-outline-secondary" ng-click="vm.voltar()" style="border-radius: 8px; padding: 12px 24px; margin-right: 15px; border: 2px solid #6c757d; color: #6c757d; font-weight: 500;">
                                    <i class="" style="margin-right: 8px;"></i>Voltar para Lista
                                </button>

                                <button type="button" class="btn btn-primary" ng-click="vm.atualizarDados()" style="border-radius: 8px; padding: 12px 24px; background-color: #1ab394; border-color: #1ab394; font-weight: 500;">
                                    <i class="fa fa-refresh" style="margin-right: 8px;"></i>Atualizar Dados
                                </button>
                            </div>
                        </div>-->

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
