(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('DetalhesPagamentoController', DetalhesPagamentoController);

    DetalhesPagamentoController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        '$stateParams',
        '$state',
        'uiGridConstants'
    ];

    function DetalhesPagamentoController(
        BaseService, 
        $rootScope, 
        toastr, 
        $scope,
        $stateParams,
        $state,
        uiGridConstants) {
        
        var vm = this;
        vm.headerItems = [
            { name: 'Movimentações' },
            { name: 'Pagamentos Antecipados', state: 'pagamentos-antecipados.index' },
            { name: 'Detalhes do Pagamento' }
        ];
        
        vm.pagamentoId = $stateParams.pagamentoId;
        vm.viagemId = $stateParams.viagemId;
        vm.pagamento = {};
        vm.carregandoPagamento = true;

        // Grid de Transações
        vm.gridTransacoes = {
            data: [],
            enableFiltering: false,
            enableHorizontalScrollbar: 1,
            enableVerticalScrollbar: 1,
            columnDefs: [
                {
                    name: 'acoes',
                    displayName: 'Ações',
                    cellTemplate: '<div class="ui-grid-cell-contents text-center">' +
                        '<button class="btn btn-xs btn-info" ng-click="grid.appScope.vm.visualizarDetalhesTransacao(row.entity)" title="Visualizar Detalhes">' +
                        '<i class="fa fa-eye"></i>' +
                        '</button>' +
                        '</div>',
                    width: 80,
                    enableSorting: false,
                    enableFiltering: false
                },
                {
                    name: 'id',
                    displayName: 'ID',
                    width: 80,
                    type: 'number'
                },
                {
                    name: 'codigoTransacao',
                    displayName: 'Código Transação',
                    width: 200
                },
                {
                    name: 'codigoTransacaoCancelamento',
                    displayName: 'Código Transação Calcelamento',
                    width: 200
                },
                {
                    name: 'status',
                    displayName: 'Status',
                    width: 120,
                    cellTemplate: '<div class="ui-grid-cell-contents">' +
                        '<span class="label" ng-class="{' +
                        '\'label-success\': row.entity.statusDescricao === \'Fechado\',' +
                        '\'label-warning\': row.entity.statusDescricao === \'Pendente\',' +
                        '\'label-danger\': row.entity.statusDescricao === \'Erro\',' +
                        '\'label-default\': row.entity.statusDescricao === \'Cancelado\',' +
                        '\'label-info\': row.entity.statusDescricao === \'Processando\'' +
                        '}">{{row.entity.statusDescricao}}</span>' +
                        '</div>'
                },
                {
                    name: 'valor',
                    displayName: 'Valor',
                    width: 120,
                    cellTemplate: '<div class="ui-grid-cell-contents">{{row.entity.valor}}</div>'
                },
                {
                    name: 'dataCriacao',
                    displayName: 'Data Criação',
                    width: 150,
                    cellFilter: 'date:"dd/MM/yyyy HH:mm"'
                },
                {
                    name: 'dataProcessamento',
                    displayName: 'Data Processamento',
                    width: 150,
                    cellFilter: 'date:"dd/MM/yyyy HH:mm"'
                },
                {
                    name: 'descricao',
                    displayName: 'Descrição',
                    width: 200
                }
            ]
        };

        // Funções
        vm.carregarPagamento = function() {
            vm.carregandoPagamento = true;
            
            BaseService.get('CentralPendencias', 'ConsultarPagamentoAntecipado', { PagamentoId: vm.pagamentoId })
                .then(function(response) {
                    vm.carregandoPagamento = false;

                    console.log('=== RESPOSTA DA API ===');
                    console.log('Response completo:', response);
                    console.log('Response.success:', response ? response.success : 'undefined');
                    console.log('Response.data:', response ? response.data : 'undefined');
                    console.log('=====================');

                    if (response && response.success && response.data) {
                        vm.pagamento = response.data;
                        console.log('Dados do pagamento carregados:', vm.pagamento);

                        // Carregar transações na grid
                        if (vm.pagamento.transacoes && vm.pagamento.transacoes.length > 0) {
                            vm.gridTransacoes.data = vm.pagamento.transacoes;
                            console.log('Transações carregadas:', vm.pagamento.transacoes);
                        } else {
                            vm.gridTransacoes.data = [];
                            console.log('Nenhuma transação encontrada para este pagamento');
                        }
                    } else {
                        console.error('Resposta inválida da API:', response);
                        toastr.error('Erro ao carregar dados do pagamento');
                        $state.go('pagamentos-antecipados.index');
                    }
                })
                .catch(function(error) {
                    vm.carregandoPagamento = false;
                    console.error('=== ERRO NA API ===');
                    console.error('Erro completo:', error);
                    console.error('Status:', error.status);
                    console.error('Data:', error.data);
                    console.error('==================');

                    toastr.error('Erro ao carregar dados do pagamento - erro de conexão');
                    // Não redireciona automaticamente, deixa o usuário decidir
                });
        };

        vm.visualizarDetalhesTransacao = function(transacao) {
            $state.go('pagamentos-antecipados.detalhes-transacao', { 
                transacaoId: transacao.id,
                pagamentoId: vm.pagamentoId,
                viagemId: vm.viagemId
            });
        };

        vm.voltar = function() {
            $state.go('pagamentos-antecipados.index');
        };

        vm.atualizarTransacoes = function() {
            vm.carregarPagamento();
        };

        vm.formatarCpfCnpj = function(value) {
            if (!value) return '';
            var digits = value.replace(/\D/g, '');
            if (digits.length <= 11) {
                return digits.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
            } else {
                return digits.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
            }
        };

        // Inicialização
        vm.carregarPagamento();
    }
})();
