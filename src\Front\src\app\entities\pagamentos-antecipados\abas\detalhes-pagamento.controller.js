(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('DetalhesPagamentoController', DetalhesPagamentoController);

    DetalhesPagamentoController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        '$stateParams',
        '$state',
        'uiGridConstants'
    ];

    function DetalhesPagamentoController(
        BaseService, 
        $rootScope, 
        toastr, 
        $scope,
        $stateParams,
        $state,
        uiGridConstants) {
        
        var vm = this;
        vm.headerItems = [
            { name: 'Movimentações' }, 
            { name: 'Pagamentos Antecipados', state: 'pagamentos-antecipados.index' }, 
            { name: 'Detalhes do Pagamento' }
        ];
        
        vm.pagamentoId = $stateParams.pagamentoId;
        vm.viagemId = $stateParams.viagemId;
        vm.pagamento = {};
        vm.carregandoPagamento = true;

        // Grid de Transações
        vm.gridTransacoes = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "Viagem/ConsultarTransacoesPagamento",
            params: function() {
                return {
                    pagamentoId: vm.pagamentoId,
                    formaPagamento: 6 // RetencaoAntecipacao
                };
            },
            dataSource: {
                url: "Viagem/ConsultarTransacoesPagamento",
                params: function() {
                    return {
                        pagamentoId: vm.pagamentoId,
                        formaPagamento: 6 // RetencaoAntecipacao
                    };
                }
            },
            columnDefs: [
                {
                    name: 'acoes',
                    displayName: 'Ações',
                    cellTemplate: '<div class="ui-grid-cell-contents text-center">' +
                        '<button class="btn btn-xs btn-primary" ng-click="grid.appScope.vm.visualizarDetalhesTransacao(row.entity)" title="Visualizar Detalhes">' +
                        '<i class="fa fa-eye"></i>' +
                        '</button>' +
                        '</div>',
                    width: 80,
                    enableSorting: false,
                    enableFiltering: false
                },
                { name: 'id', displayName: 'Código', width: 100 },
                { 
                    name: 'status', 
                    displayName: 'Status',
                    cellTemplate: '<div class="ui-grid-cell-contents">' +
                        '<span ng-class="{\'label label-success\': row.entity.status === \'Baixado\', ' +
                        '\'label label-warning\': row.entity.status === \'Pendente\', ' +
                        '\'label label-danger\': row.entity.status === \'NaoExecutado\', ' +
                        '\'label label-default\': row.entity.status === \'Cancelado\'}">' +
                        '{{row.entity.statusDescricao}}' +
                        '</span>' +
                        '</div>',
                    width: 120
                },
                { 
                    name: 'valor', 
                    displayName: 'Valor',
                    cellFilter: 'currency:"R$ "',
                    width: 120
                },
                { name: 'idContaOrigem', displayName: 'Id Conta Origem', width: 150 },
                { name: 'idContaDestino', displayName: 'Id Conta Destino', width: 150 },
                { name: 'dataBaixa', displayName: 'Data Baixa', width: 150 },
                { name: 'dataCadastro', displayName: 'Data Cadastro', width: 150 },
                { name: 'codigoTransacao', displayName: 'Código Transação', width: 200 },
                { name: 'motivoPendencia', displayName: 'Motivo Pendência', width: 200 }
            ]
        };

        // Funções
        vm.carregarPagamento = function() {
            vm.carregandoPagamento = true;
            
            BaseService.get('PagamentoEvento', 'ConsultarPorId', { idPagamentoEvento: vm.pagamentoId })
                .then(function(response) {
                    vm.carregandoPagamento = false;
                    
                    if (response && response.success) {
                        vm.pagamento = response.data;
                    } else {
                        toastr.error('Erro ao carregar dados do pagamento');
                        $state.go('pagamentos-antecipados.index');
                    }
                })
                .catch(function(error) {
                    vm.carregandoPagamento = false;
                    toastr.error('Erro ao carregar dados do pagamento');
                    $state.go('pagamentos-antecipados.index');
                });
        };

        vm.visualizarDetalhesTransacao = function(transacao) {
            $state.go('pagamentos-antecipados.detalhes-transacao', { 
                transacaoId: transacao.id,
                pagamentoId: vm.pagamentoId,
                viagemId: vm.viagemId
            });
        };

        vm.voltar = function() {
            $state.go('pagamentos-antecipados.index');
        };

        vm.atualizarTransacoes = function() {
            if (vm.gridTransacoes && vm.gridTransacoes.dataSource && vm.gridTransacoes.dataSource.refresh) {
                vm.gridTransacoes.dataSource.refresh();
            } else {
                console.log('Grid de transações não está pronto para atualização');
            }
        };

        // Inicialização
        vm.carregarPagamento();
    }
})();
