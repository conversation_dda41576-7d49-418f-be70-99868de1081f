<style>
    #ClientSecretConsultaFreteCrudController .fixLRpg {
        padding-left: 4px !important;
        padding-right: 4px !important;
        text-align: -webkit-center;
    }

    .widthHF {
        width: 25px;
    }

    .colorGreen {
        color: green;
    }

    .form-wizard>ol>li {
        min-height: 50px;
        padding-top: 10px;
        padding-bottom: 7px;
        background-color: #eeeeee;
        border-color: #eaeaea;
        border-style: solid;
        cursor: pointer;
        border-width: 2px;
    }

    #ClientSecretConsultaFreteCrudController .imgPassCombination {
        position: relative;
        top: 5px;
        right: -37px;
    }

    #ClientSecretConsultaFreteCrudController .ui-select-bootstrap .ui-select-toggle>a.btn {
        position: absolute !important;
        height: 10px !important;
        right: 10px !important;
        margin-top: 0px !important;
    }

    #ClientSecretConsultaFreteCrudController .ui-select-bootstrap .ui-select-toggle>a.btn>i {
        margin-top: -5px !important;
    }

    #ClientSecretConsultaFreteCrudController .ui-select-bootstrap .ui-select-choices {
        width: 100% !important;
    }

    #ClientSecretConsultaFreteCrudController .ui-select-bootstrap .ui-select-choices-row>span {
        cursor: pointer;
        display: block;
        padding: 3px 20px;
    }

    #ClientSecretConsultaFreteCrudController .ui-select-bootstrap .ui-select-choices-row.active>span {
        background-color: #337ab7;
        color: #fff;
    }

    #ClientSecretConsultaFreteCrudController .ui-select-bootstrap .ui-select-choices-row.disabled>span,
    #ClientSecretConsultaFreteCrudController .ui-select-bootstrap .ui-select-choices-row.active.disabled>span {
        color: #999;
        cursor: not-allowed;
        background-color: #fff;
    }

    .form-wizard>ol>li {
        min-height: 33px;
        padding-top: 2px;
        padding-bottom: 0px;
        background-color: #eeeeee;
        border-color: #eaeaea;
        border-style: solid;
        cursor: pointer;
        border-width: 2px;
    }
</style>
<div id="ClientSecretConsultaFreteCrudController" ng-controller="ClientSecretConsultaFreteCrudController as vm">
    <form-header ng-submit="return" items="vm.headerItems" head="'Client Secret Consulta Frete'"></form-header>
    <div class="wrapper-content animated overflow-hidden fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Novo' : 'Editar'}} Client Secret Consulta Frete</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="form" role="form" novalidate ats-validator ng-submit="vm.save(form)" show-validation>
                            <div form-wizard steps="1">
                                <div class="form-wizard">
                                    <ol class="row">
                                        <li ng-click="wizard.go(1)" class="fixLRpg col-sm-12" ng-class="{'active': wizard.active(1)}">
                                            <h4>Principal</h4>
                                        </li>
                                    </ol>
                                    <div class="tab-content">
                                        <div id="step1" class="tab-pane" ng-class="{'active': wizard.active(1)}">
                                            <div ng-include="'app/entities/client-secret-consulta-frete/abas/principal.html'"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <div class="row">
                                         <div class="col-md-12 col-lg-12 text-right">
                                            <button type="button" ng-click="vm.voltar()" class="btn btn-labeled
                                            btn-default">
                                                <i class="fa fa-arrow-left"></i> Voltar
                                            </button>
                                             <button type="submit" ng-disabled="vm.isSaving" class="btn btn-labeled btn-success text-right" disabled="disabled">
                                                <i class="fa fa-save"></i> {{vm.isNew() ? 'Salvar' : 'Atualizar'}}
                                                <span ng-show="vm.isSaving">
                                                    <i class="fa fa-spinner fa-spin"></i>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
