(function (){
    'use strict';

    angular.module('bbcWeb.usuario-frota.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('usuario-frota', {
            abstract: true,
            url: "/usuario-frota",
            templateUrl: "app/layout/content.html"
        }).state('usuario-frota.index', {
            url: '/index',
            templateUrl: 'app/entities/usuario-frota/usuario-frota.html'
        }).state('usuario-frota.usuario-frota-crud', {
            url: '/:link',
            templateUrl: 'app/entities/usuario-frota/usuario-frota-crud.html'
        });
    }
})();
