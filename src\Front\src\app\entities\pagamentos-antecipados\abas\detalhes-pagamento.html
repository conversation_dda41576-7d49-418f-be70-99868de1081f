<div id="DetalhesPagamentoController" ng-controller="DetalhesPagamentoController as vm">
    <form-header items="vm.headerItems" head="'Detalhes do Pagamento'"></form-header>
    
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins" style="border-radius: 8px;">


                    <div class="ibox-title-2x"
                        style="display: flex; align-items: center; justify-content: space-between; min-height: 50px; padding: 20px 20px 20px 20px;">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <h5 style="margin: 0; line-height: 1;">
                                Detalhes do Pagamento #{{vm.pagamentoId}}
                            </h5>

                            <span ng-if="!vm.carregandoPagamento && vm.pagamento.statusDescricao" class="label" ng-class="{
                                'label-success': vm.pagamento.status === 0 || vm.pagamento.statusDescricao === 'Fechado' || vm.pagamento.statusDescricao === 'Aprovado',
                                'label-warning': vm.pagamento.status === 1 || vm.pagamento.statusDescricao === 'Aberto',
                                'label-info': vm.pagamento.status === 2 || vm.pagamento.statusDescricao === 'Pendente',
                                'label-danger': vm.pagamento.status === 3 || vm.pagamento.statusDescricao === 'Erro',
                                'label-default': vm.pagamento.status === 4 || vm.pagamento.statusDescricao === 'Cancelado',
                                'label-primary': vm.pagamento.status === 5 || vm.pagamento.statusDescricao === 'Processando',
                                'label-muted': vm.pagamento.status === 6 || vm.pagamento.statusDescricao === 'Não Executado'
                            }" style="font-size: 12px; vertical-align: middle; line-height: 1; align-self: center;">
                                {{vm.pagamento.statusDescricao}}
                            </span>

                            <span class="label" ng-class="{
                                                'label-primary': vm.pagamento.statusAntecipacaoDescricao === 'Disponivel',
                                                'label-info': vm.pagamento.statusAntecipacaoDescricao === 'AguardandoProcessamento',
                                                'label-success': vm.pagamento.statusAntecipacaoDescricao === 'Aprovado',
                                                'label-danger': vm.pagamento.statusAntecipacaoDescricao === 'Erro',
                                                'label-default': vm.pagamento.statusAntecipacaoDescricao === 'Não Disponível'
                                            }" style="font-size: 12px;">{{vm.pagamento.statusAntecipacaoDescricao}}
                            </span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">


                            <button type="button"style="border-radius: 6px; font-weight: 500;" ng-disabled="vm.isSaving" ng-click="vm.voltar()" class="btn btn-labeled btn-default">
                                <span class="btn-label">
                                    <i class="fa fa-arrow-circle-left"></i>
                                </span>
                                Voltar
                            </button>

                            
                           
                        </div>
                        
                    </div>   

                    <div class="ibox-content" ng-show="vm.carregandoPagamento">
                        <div class="text-center">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                            <p class="text-muted">Carregando dados do pagamento...</p>
                        </div>
                    </div>

                    <div class="ibox-content" ng-show="!vm.carregandoPagamento" style="background-color: #f8f9fa; padding: 30px;">

                        <div class="row">
                            <!-- Card: Informações do Pagamento -->
                            <div class="col-lg-8 col-md-12">
                                <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                                    <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                        <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center;">
                                            <i class="fa fa-info-circle" style="color: #1ab394; margin-right: 10px; font-size: 18px;"></i>
                                            Informações do Pagamento
                                        </h5>
                                    </div>
                                    <div class="card-body" style="padding: 25px;">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Código</label>
                                                    <div style="color: #495057; font-weight: 500;">{{vm.pagamento.id}}</div>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Viagem ID</label>
                                                    <div style="color: #495057; font-weight: 500;">{{vm.pagamento.viagemId}}</div>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Viagem ID Externo</label>
                                                    <div style="color: #495057; font-weight: 500;">{{vm.pagamento.viagemExternoId}}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Valor</label>
                                                    <div style="color: #28a745; font-weight: 700; font-size: 16px;">{{vm.pagamento.valor}}</div>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Tipo</label>
                                                    <div style="color: #495057; font-weight: 500;">{{vm.pagamento.tipoDescricao}}</div>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Forma</label>
                                                    <div style="color: #495057; font-weight: 500;">{{vm.pagamento.formaPagamentoDescricao}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Card: Datas Principais -->
                            <div class="col-lg-4 col-md-12 col-sm-12">
                                <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                                    <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                        <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center;">
                                            <i class="fa fa-calendar" style="color: #f39c12; margin-right: 10px; font-size: 18px;"></i>
                                            Datas Principais
                                        </h5>
                                    </div>
                                    <div class="card-body" style="padding: 25px;">
                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Previsão Pagamento</label>
                                            <div style="color: #7b8691; font-weight: 700; font-size: 16px;">{{vm.pagamento.dataPrevisaoPagamento}}</div>
                                        </div>

                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Data Cadastro</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.dataCadastro}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Card: Informações do Proprietário -->
                            <div class="col-lg-4 col-md-6 col-sm-12">
                                <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                                    <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                        <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center;">
                                            <i class="fa fa-user" style="color: #3498db; margin-right: 10px; font-size: 18px;"></i>
                                            Informações do Proprietário
                                        </h5>
                                    </div>
                                    <div class="card-body" style="padding: 25px;">
                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Nome</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.nomeProprietario}}</div>
                                        </div>

                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">CPF/CNPJ</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.formatarCpfCnpj(vm.pagamento.cpfCnpjProprietario)}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Card: Informações da Empresa -->
                            <div class="col-lg-4 col-md-6 col-sm-12">
                                <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                                    <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                        <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center;">
                                            <i class="fa fa-building" style="color: #9b59b6; margin-right: 10px; font-size: 18px;"></i>
                                            Informações da Empresa
                                        </h5>
                                    </div>
                                    <div class="card-body" style="padding: 25px;">
                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Razão Social</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.razaoSocialEmpresa}}</div>
                                        </div>

                                        <div style="margin-bottom: 20px;">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">CNPJ</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.formatarCpfCnpj(vm.pagamento.cnpjEmpresa)}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Card: Informações de Antecipação -->
                            <div class="col-lg-4 col-md-12 col-sm-12">
                                <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                                    <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                        <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center;">
                                            <i class="fa fa-solid fa-tags" style="color: #e67e22; margin-right: 10px; font-size: 18px;"></i>
                                            Status
                                        </h5>
                                    </div>
                                    <div class="card-body" style="padding: 25px;">


                                          <div style="margin-bottom: 20px;" ng-if="vm.pagamento.statusDescricao">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Pagamento</label>
                                            <span ng-if="!vm.carregandoPagamento && vm.pagamento.statusDescricao" class="label" ng-class="{
                                                'label-success': vm.pagamento.status === 0 || vm.pagamento.statusDescricao === 'Fechado' || vm.pagamento.statusDescricao === 'Aprovado',
                                                'label-warning': vm.pagamento.status === 1 || vm.pagamento.statusDescricao === 'Aberto',
                                                'label-info': vm.pagamento.status === 2 || vm.pagamento.statusDescricao === 'Pendente',
                                                'label-danger': vm.pagamento.status === 3 || vm.pagamento.statusDescricao === 'Erro',
                                                'label-default': vm.pagamento.status === 4 || vm.pagamento.statusDescricao === 'Cancelado',
                                                'label-primary': vm.pagamento.status === 5 || vm.pagamento.statusDescricao === 'Processando',
                                                'label-muted': vm.pagamento.status === 6 || vm.pagamento.statusDescricao === 'Não Executado'
                                            }" style="font-size: 12px;">
                                                {{vm.pagamento.statusDescricao}}
                                            </span>
                                        </div>


                                        <div style="margin-bottom: 20px;" ng-if="vm.pagamento.statusAntecipacaoDescricao">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Antecipação</label>
                                            <span class="label" ng-class="{
                                                'label-primary': vm.pagamento.statusAntecipacaoDescricao === 'Disponivel',
                                                'label-info': vm.pagamento.statusAntecipacaoDescricao === 'AguardandoProcessamento',
                                                'label-success': vm.pagamento.statusAntecipacaoDescricao === 'Aprovado',
                                                'label-danger': vm.pagamento.statusAntecipacaoDescricao === 'Erro',
                                                'label-default': vm.pagamento.statusAntecipacaoDescricao === 'Não Disponível'
                                            }" style="font-size: 12px;">{{vm.pagamento.statusAntecipacaoDescricao}}</span>
                                        </div>

                                        <div style="margin-bottom: 20px;" ng-if="vm.pagamento.antecipacaoMotivo">
                                            <label style="color: #6c757d; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 5px; display: block;">Motivo da Antecipação</label>
                                            <div style="color: #495057; font-weight: 500;">{{vm.pagamento.antecipacaoMotivo}}</div>
                                        </div>

                                        <div ng-if="!vm.pagamento.statusAntecipacaoDescricao && !vm.pagamento.antecipacaoMotivo" style="color: #6c757d; font-style: italic; text-align: center; padding: 20px 0;">
                                            Nenhuma informação de antecipação disponível
                                        </div>
                                      
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Card: Transações do Pagamento -->
                        <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border: none;">
                            <div class="card-header" style="background: white; border-bottom: 1px solid #e9ecef; border-radius: 8px 8px 0 0; padding: 20px 25px;">
                                <h5 style="margin: 0; color: #495057; font-weight: 600; display: flex; align-items: center; justify-content: space-between;">
                                    <span style="display: flex; align-items: center;">
                                        <i class="fa fa-exchange" style="color: #2c3e50; margin-right: 10px; font-size: 18px;"></i>
                                        Transações do Pagamento
                                    </span>
                                    <button type="button" class="btn btn-success btn-sm" ng-click="vm.atualizarTransacoes()" 
                                        style="border-radius: 6px; font-weight: 500;">
                                        <i class="fa fa-refresh"></i>
                                        <span class="hidden-xs ml-5">Atualizar</span>
                                    </button>
                                </h5>
                            </div>
                            <div class="card-body" style="padding: 25px;">
                                <!-- Grid de Transações -->
                                <div ng-if="vm.gridTransacoes.data && vm.gridTransacoes.data.length > 0">
                                    <div ui-grid="vm.gridTransacoes"
                                         class="grid"
                                         style="height: 400px; width: 100%;"
                                         ui-grid-resize-columns
                                         ui-grid-move-columns
                                         ui-grid-pagination
                                         ui-grid-selection>
                                    </div>
                                </div>

                                <!-- Mensagem quando não há transações -->
                                <div ng-if="!vm.gridTransacoes.data || vm.gridTransacoes.data.length === 0"
                                     style="text-align: center; padding: 60px 20px; color: #6c757d;">
                                    <i class="fa fa-info-circle" style="font-size: 48px; color: #dee2e6; margin-bottom: 20px;"></i>
                                    <h4 style="color: #6c757d; font-weight: 500; margin-bottom: 10px;">Nenhuma transação encontrada</h4>
                                    <p style="color: #adb5bd; margin: 0;">Não há transações registradas para este pagamento.</p>
                                </div>
                            </div>
                        </div>

                        <!--<div class="row">
                            <div class="form-group">
                                <div class="col-md-12 col-lg-12 text-right">
                                    <button type="button"style="border-radius: 6px; font-weight: 500;" ng-disabled="vm.isSaving" ng-click="vm.voltar()" class="btn btn-labeled btn-default">
                                        <span class="btn-label">
                                            <i class="fa fa-arrow-circle-left"></i>
                                        </span>
                                        Voltar
                                    </button>
                                </div>
                            </div>
                        </div>-->

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
