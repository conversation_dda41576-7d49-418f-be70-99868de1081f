<div id="DetalhesPagamentoController" ng-controller="DetalhesPagamentoController as vm">
    <form-header items="vm.headerItems" head="'Detalhes do Pagamento'"></form-header>
    
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Detalhes do Pagamento #{{vm.pagamentoId}}</h5>
                        <div ibox-tools></div>
                    </div>
                    
                    <div class="ibox-content">
                        <!-- Loading -->
                        <div ng-show="vm.carregandoPagamento" class="text-center">
                            <div class="spiner-example">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                    <div class="sk-rect6"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Dados do Pagamento -->
                        <div ng-show="!vm.carregandoPagamento">
                            <div class="row">
                                <div class="col-md-12">
                                    <button type="button" 
                                            class="btn btn-default pull-right" 
                                            ng-click="vm.voltar()">
                                        <i class="fa fa-arrow-left"></i> Voltar
                                    </button>
                                    <h4>Informações do Pagamento</h4>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Código do Pagamento:</label>
                                        <p class="form-control-static">{{vm.pagamento.id}}</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Código da Viagem:</label>
                                        <p class="form-control-static">{{vm.viagemId}}</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Status:</label>
                                        <p class="form-control-static">
                                            <span ng-class="{
                                                'label label-success': vm.pagamento.status === 'Fechado', 
                                                'label label-warning': vm.pagamento.status === 'Aberto',
                                                'label label-danger': vm.pagamento.status === 'Cancelado'
                                            }">
                                                {{vm.pagamento.statusDescricao}}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Valor:</label>
                                        <p class="form-control-static">
                                            <strong>{{vm.pagamento.valor | currency:"R$ "}}</strong>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Status Antecipação:</label>
                                        <p class="form-control-static">
                                            <span ng-class="{
                                                'label label-info': vm.pagamento.statusAntecipacaoParcelaProprietario === 'AguardandoProcessamento', 
                                                'label label-success': vm.pagamento.statusAntecipacaoParcelaProprietario === 'Aprovado',
                                                'label label-danger': vm.pagamento.statusAntecipacaoParcelaProprietario === 'Erro'
                                            }">
                                                {{vm.pagamento.statusAntecipacaoDescricao}}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Data Previsão Pagamento:</label>
                                        <p class="form-control-static">{{vm.pagamento.dataPrevisaoPagamento}}</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Data Cadastro:</label>
                                        <p class="form-control-static">{{vm.pagamento.dataCadastro}}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>Descrição/Motivo:</label>
                                        <p class="form-control-static">{{vm.pagamento.antecipacaoMotivo || 'Não informado'}}</p>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- Grid de Transações -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="pull-right">
                                        <button tooltip-placement="top" 
                                                ng-click="vm.atualizarTransacoes();" 
                                                uib-tooltip="Atualizar Transações" 
                                                type='button' 
                                                class="btn btn-labeled btn-default">
                                            <i class="fa fa-refresh"></i>
                                            <span class="pl-5">Atualizar</span>
                                        </button>
                                    </div>
                                    <h4>Transações do Pagamento</h4>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div ui-grid="vm.gridTransacoes" 
                                         ng-style="{height: vm.gridTransacoes.getGridHeight()}" 
                                         class="grid" 
                                         style="width: 100%;" 
                                         ui-grid-pinning 
                                         ui-grid-save-state
                                         ui-grid-pagination 
                                         ui-grid-auto-resize 
                                         ui-grid-resize-columns>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
